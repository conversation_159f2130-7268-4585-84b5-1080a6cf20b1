<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>

    <link rel="stylesheet" href="/css/style_game1.css">
    <script src="/js/game1.js" defer></script>
</head>
<body>
    <div class="container">
        <div class="header">
          <!--   <p class="score-lbl">Score: <p class="score-txt">0</p></p> -->
            <a href="mainpage.html">&times;</a>
        </div>

        <div class="box-con">

            <div class="left-con">
                <div class="item">
                    <p>Router</p>
                    <img class="img" id="img0" src="/images/game1/router1.png" alt="Router" srcset="">
                </div>

                <div class="item">
                    <p>Switch</p>
                    <img class="img" id="img1"  src="/images/game1/switch1.png" alt="Switch" srcset="">
                </div>

                <div class="item">
                    <p>Hub</p>
                    <img class="img" id="img2"  src="/images/game1/hub1.png" alt="Hub" srcset="">
                </div>

                <div class="item">
                    <p>Modem</p>
                    <img class="img" id="img3"  src="/images/game1/modem1.png" alt="Modem" srcset="">
                </div>

                <div class="item">
                    <p>Access Point</p>
                    <img class="img" id="img4"  src="/images/game1/AP1.png" alt="Access Point" srcset="">
                </div>

                <div class="item">
                    <p>Firewall</p>
                    <img class="img" id="img5"  src="/images/game1/firewall1.png" alt="Firewall" srcset="">
                </div>

                <div class="item">
                    <p>Network Card</p>
                    <img class="img" id="img6"  src="/images/game1/networkcad1.png" alt="Network card" srcset="">
                </div>

                <div class="item">
                    <p>Repeater</p>
                    <img class="img" id="img7"  src="/images/game1/repeater1.png" alt="Repeater" srcset="">
                </div>

                <div class="item">
                    <p>Bridge</p>
                    <img class="img" id="img8"  src="/images/game1/bridge1.png" alt="Bridge" srcset="">
                </div>

                <div class="item">
                    <p>Gateway</p>
                    <img class="img" id="img9"  src="/images/game1/gateway1.png" alt="Gateway" srcset="">
                </div>
            </div>

            <div class="right-con">
                <div class="right-con-bin">
                    <div class="item-bin">
                        <span class="image-con droppable" id=box0>

                        </span>
                    </div>

                    <div class="description-bin">
                        <p class="description">Connects different networks and routes data between them.</p>
                    </div>
                </div>

                <div class="right-con-bin">
                    <div class="item-bin">
                        <span class="image-con droppable" id="box1">

                        </span>
                    </div>

                    <div class="description-bin">
                        <p class="description">Connects multiple devices in a LAN and forwards data based on the specific target.</p>
                    </div>
                </div>

                <div class="right-con-bin">
                    <div class="item-bin">
                        <span class="image-con droppable" id="box2">

                        </span>
                    </div>

                    <div class="description-bin">
                        <p class="description">A simple device that broadcasts data to all devices in a network.</p>
                    </div>
                </div>

                <div class="right-con-bin">
                    <div class="item-bin">
                        <span class="image-con droppable" id="box3">

                        </span>
                    </div>

                    <div class="description-bin">
                        <p class="description">Converts digital data to analog for transmission over phone lines.</p>
                    </div>
                </div>

                <div class="right-con-bin">
                    <div class="item-bin">
                        <span class="image-con droppable" id="box4">

                        </span>
                    </div>

                    <div class="description-bin">
                        <p class="description">Enables wireless devices to connect to wired network through Wi-Fi.</p>
                    </div>
                </div>

                <div class="right-con-bin">
                    <div class="item-bin">
                        <span class="image-con droppable" id="box5">

                        </span>
                    </div>

                    <div class="description-bin">
                        <p class="description">A security system that monitors and controls incoming/outgoing traffic.</p>
                    </div>
                </div>

                <div class="right-con-bin">
                    <div class="item-bin">
                        <span class="image-con droppable" id="box6">

                        </span>
                    </div>

                    <div class="description-bin">
                        <p class="description">A hardware component that allows a device to connect to a network.</p>
                    </div>
                </div>

                <div class="right-con-bin">
                    <div class="item-bin">
                        <span class="image-con droppable" id="box7">

                        </span>
                    </div>

                    <div class="description-bin">
                        <p class="description">Amplifies or regenerates signals to extend network range.</p>
                    </div>
                </div>

                <div class="right-con-bin">
                    <div class="item-bin">
                        <span class="image-con droppable" id="box8">

                        </span>
                    </div>

                    <div class="description-bin">
                        <p class="description">A device that connects two network segments.</p>
                    </div>
                </div>

                <div class="right-con-bin">
                    <div class="item-bin">
                        <span class="image-con droppable" id="box9">

                        </span>
                    </div>

                    <div class="description-bin">
                        <p class="description">A device that connects two different networks using different protocols.</p>
                    </div>
                </div>
            </div>

            <button class="btn-submit" type="button">Submit</button>
        </div>
    </div>

</body>
</html>