function shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]]; // Swap elements
    }
    return array;
}

// Function to randomize the order of right-con-bin elements
function randomizeRightConBins() {
    const rightCon = document.querySelector('.right-con');
    const bins = Array.from(rightCon.children); // Convert HTMLCollection to an array

    // Shuffle the bins array
    const shuffledBins = shuffleArray(bins);

    // Clear the right-con container
    rightCon.innerHTML = '';

    // Append the shuffled bins back to the right-con container
    shuffledBins.forEach(bin => rightCon.appendChild(bin));
}

document.addEventListener('dragstart', function(event) {
    // Access the element being dragged
    const draggedElement = event.target;
    
    // Get the ID of the dragged element
    const elementId = draggedElement.id;
    
    // Log the ID or use it as needed
    console.log('Element ID:', elementId);
    
    // Optionally, store the ID in the dataTransfer object for use during drop
    event.dataTransfer.setData('text/plain', elementId);
});

document.addEventListener('dragover', function(event) {
    // Prevent default to allow drop
    event.preventDefault();

    

});

document.addEventListener('dragenter', function (event) {
    // Check if the drop target has the class 'droppable'
    if (event.target.classList.contains('droppable')) {
        event.target.classList.add('dragenter'); // Add a class for visual feedback
    }
});

// Remove the class when the draggable element leaves the drop target
document.addEventListener('dragleave', function (event) {
    // Check if the drop target has the class 'droppable'
    if (event.target.classList.contains('droppable')) {
        event.target.classList.remove('dragenter'); // Remove the class only if no image is present
    }
});

document.addEventListener('drop', function(event) {
    // Prevent default behavior (e.g., opening as a link)
    event.preventDefault();
    
    // Retrieve the ID from the dataTransfer object
    const elementId = event.dataTransfer.getData('text/plain');
    
    // Log the ID or use it as needed
    console.log('Dropped Element ID:', elementId);

    
    // Check if the drop target has the class 'droppable'
    if (event.target.classList.contains('droppable') || event.target.classList.contains('item')) {
        // Optionally, append the dragged element to the drop zone
        const draggedElement = document.getElementById(elementId);
        event.target.classList.add("dropped");
        event.target.appendChild(draggedElement);
        
        // Set up a MutationObserver to monitor the span for changes
        const observer = new MutationObserver(function (mutationsList) {
            for (const mutation of mutationsList) {
                if (mutation.type === 'childList') {
                    // Check if the img tag is no longer a child of the span
                    if (!event.target.querySelector('img')) {
                        event.target.classList.remove('dropped');
                        observer.disconnect(); // Stop observing once the class is removed
                    }
                }
            }
        });

        // Start observing the span for child node changes
        observer.observe(event.target, { childList: true });
    } else {
        console.log('Drop target is not droppable.');
    }

    // Remove the class only if no image is present
    if (event.target.classList.contains('droppable')) {
        event.target.classList.remove('dragenter');
    }
});

// Function to check if the correct image is placed in the correct box
function checkAnswersAndUpdateScore() {
    // Validate if all boxes contain an image
    if (!validateBoxes()) {
        showValidationModal();
        return; // Exit the function if validation fails
    }

    let score = 0;

    // Loop through each box and check if the correct image is placed
    for (let i = 0; i <= 6; i++) {
        const box = document.getElementById(`box${i}`);
        const image = box.querySelector('img');

        // Check if the image exists and has the correct ID
        if (image && image.id === `img${i}`) {
            score++; // Increment the score if the correct image is in the box
        }
    }

    // Calculate the percentage score
    const totalQuestions = 7; // Total number of boxes
    const percentageScore = (score / totalQuestions) * 100;

    // Display the modal with the percentage score
    showModal(percentageScore);
}

// Function to check if all boxes contain an image
function validateBoxes() {
    for (let i = 0; i <= 6; i++) {
        const box = document.getElementById(`box${i}`);
        const image = box.querySelector('img');

        // If any box is missing an image, return false
        if (!image) {
            return false;
        }
    }
    // If all boxes have an image, return true
    return true;
}

// Function to show a modal with a custom message
function showValidationModal(message) {
    // Create the overlay element
    const overlay = document.createElement('div');
    overlay.classList.add('overlay'); // Add a class for styling
    document.body.appendChild(overlay);

    // Create the modal element
    const modal = document.createElement('div');
    modal.classList.add('modal'); // Add a class for styling
    modal.style.textAlign = 'center';
    modal.style.backgroundColor = "#0b0b23";
    modal.style.color = "rgb(255, 255, 255)";
    modal.style.fontFamily = "Arial";
    modal.style.fontSize = "1.5em ";
    modal.style.padding = "20px";
    modal.style.boxShadow = "0 0 4px rgba(203, 193, 193, 0.48)";
    modal.innerHTML = `
    <p style="font-size: 2.5em;">😠</p><br>
    <p>Complete the challenge before you submit.</p><br>
    `;

    // Add an "Ok" button to the modal
    const okButton = document.createElement('button');
    okButton.textContent = 'Ok';
    okButton.style.marginTop = '10px';
    okButton.style.padding = '10px 20px';
    okButton.style.scrollMarginTop = '20px';
    okButton.style.borderRadius = '5px';
    okButton.style.fontFamily = 'Arial';
    okButton.style.fontSize = '.9em';
    okButton.style.border = 'none';
    okButton.style.backgroundColor = '#bfa903f2';
    okButton.style.color = 'white';
    okButton.style.cursor = 'pointer';
    okButton.addEventListener('click', () => {
        // Remove the modal and overlay when the "Ok" button is clicked
        document.body.removeChild(modal);
        document.body.removeChild(overlay);
    });
    modal.appendChild(okButton);

    // Append the modal to the body
    document.body.appendChild(modal);

    // Disable all other elements on the page
    const allElements = document.querySelectorAll('body > *:not(div)');
    allElements.forEach(element => {
        element.style.pointerEvents = 'none'; // Disable interactions
        element.style.filter = 'blur(2px)'; // Optional: Add a blur effect
    });
}

// Function to show the instruction modal
function showInstructionModal() {
    // Create the overlay element
    const overlay = document.createElement('div');
    overlay.classList.add('overlay');
    document.body.appendChild(overlay);

    // Create the modal element
    const modal = document.createElement('div');
    modal.classList.add('modal');
    modal.style.textAlign = 'center';
    modal.style.backgroundColor = "#0b0b23";
    modal.style.color = "rgb(255, 255, 255)";
    modal.style.padding = "20px";
    modal.style.boxShadow = "0 0 4px rgba(203, 193, 193, 0.48)";
    modal.style.maxWidth = "600px";
    modal.style.margin = "auto";

    // Add the title to the modal
    const title = document.createElement('h2');
    title.textContent = 'Mission';
    title.style.color = "gold";
    title.style.marginBottom = "20px";
    modal.appendChild(title);

    // Add the instructions content
    const instructions = document.createElement('div');
    instructions.style.textAlign = 'left';
    instructions.style.marginBottom = '20px';
    instructions.innerHTML = `
        <h3>Mactching Challenge</h3><br>
            <h4>🎮 How to Play:</h4><br>
                <p><span class="emoji">➤</span> Look at the cable images displayed above</p><br>
                <p><span class="emoji">➤</span> Drag and drop them into the correct boxes below!</p><br>
                <p><span class="emoji">➤</span> Match them accurately to complete the level</p><br>
                <p><span class="emoji">🏆</span> Earn up to 3 stars based on your performance!</p><br>

            <p><span class="emoji">💡</span> <strong>Pro Tip:</strong> Read and match carefully!</p>
    `;
    instructions.style.fontFamily = "Arial";
    instructions.style.fontSize = "1.2em";
    modal.appendChild(instructions);

    // Add an "Ok" button to the modal
    const okButton = document.createElement('button');
    okButton.textContent = 'Start Game';
    okButton.style.marginTop = '10px';
    okButton.style.padding = '10px 20px';
    okButton.style.borderRadius = '5px';
    okButton.style.border = 'none';
    okButton.style.backgroundColor = '#007BFF';
    okButton.style.color = 'white';
    okButton.style.cursor = 'pointer';
    okButton.addEventListener('click', () => {
        // Remove the modal and overlay when the "Ok" button is clicked
        document.body.removeChild(modal);
        document.body.removeChild(overlay);
        
        // Enable interactions on the page
        const allElements = document.querySelectorAll('body > *:not(div)');
        allElements.forEach(element => {
            element.style.pointerEvents = 'auto';
            element.style.filter = 'none';
        });
    });
    modal.appendChild(okButton);

    // Append the modal to the body
    document.body.appendChild(modal);

    // Disable all other elements on the page temporarily
    const allElements = document.querySelectorAll('body > *:not(div)');
    allElements.forEach(element => {
        element.style.pointerEvents = 'none';
        element.style.filter = 'blur(2px)';
    });
}

//Disable Hightlighting text
document.body.style.userSelect = 'none';

// Show the instruction modal when the page loads
document.addEventListener('DOMContentLoaded', function() {
    randomizeRightConBins(); // Randomize the bins first
    showInstructionModal(); // Then show instructions

    const images = document.querySelectorAll('.img');
    
    // Create a tooltip element
    const tooltip = document.createElement('div');
    tooltip.style.position = 'absolute';
    tooltip.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
    tooltip.style.color = 'white';
    tooltip.style.padding = '5px 10px';
    tooltip.style.borderRadius = '5px';
    tooltip.style.zIndex = '1000';
    tooltip.style.display = 'none';
    tooltip.style.fontFamily = 'Arial, sans-serif';
    tooltip.style.fontSize = '14px';
    document.body.appendChild(tooltip);
    
    // Add event listeners to each image
    images.forEach(img => {
        img.addEventListener('mouseenter', function(e) {
            // Check if the image is inside an element with class 'image-con'
            if (!this.closest('.image-con')) return;
            
            // Get the alt text or use a default if empty
            const altText = this.alt || 'No description available';
            
            // Position the tooltip near the cursor
            tooltip.textContent = altText;
            tooltip.style.display = 'block';
            tooltip.style.left = (e.pageX + 10) + 'px';
            tooltip.style.top = (e.pageY + 10) + 'px';
        });
        
        img.addEventListener('mouseleave', function() {
            tooltip.style.display = 'none';
        });
        
        img.addEventListener('mousemove', function(e) {
            // Only update if inside image-con
            if (!this.closest('.image-con')) return;
            
            // Update tooltip position as mouse moves
            tooltip.style.left = (e.pageX + 10) + 'px';
            tooltip.style.top = (e.pageY + 10) + 'px';
        });
    });
});

// Function to show a modal with the percentage score and star rating
function showModal(percentageScore) {
    // Determine star rating based on percentage
    let stars;
    let performanceLevel;
    
    if (percentageScore >= 80) {
        stars = '⭐⭐⭐';
        performanceLevel = 'Perfect!';
    } else if (percentageScore >= 50) {
        stars = '⭐⭐';
        performanceLevel = 'Almost Perfect!';
    } else {
        stars = '⭐';
        performanceLevel = 'Level Cleared! Keep practicing!';
    }

    // Create the overlay element
    const overlay = document.createElement('div');
    overlay.classList.add('overlay');
    document.body.appendChild(overlay);

    // Create the modal element
    const modal = document.createElement('div');
    modal.classList.add('modal');
    modal.style.minWidth = "25vw";
    modal.style.textAlign = 'center';
    modal.style.backgroundColor = "#0b0b23";
    modal.style.color = "rgb(255, 255, 255)";
    modal.style.padding = "20px";
    modal.style.boxShadow = "0 0 4px rgba(203, 193, 193, 0.48)";

    // Add the star rating to the modal
    const starsText = document.createElement('p');
    starsText.style.color = "gold";
    starsText.style.fontSize = "3em";
    starsText.style.margin = "0";
    starsText.textContent = stars;
    modal.appendChild(starsText);

    // Add performance level text
    const performanceText = document.createElement('p');
    performanceText.style.color = "rgb(255, 255, 255)";
    performanceText.style.fontFamily = "Arial, sans-serif";
    performanceText.style.fontSize = "1.5rem";
    performanceText.style.marginTop = "15px";
    performanceText.textContent = performanceLevel;
    modal.appendChild(performanceText);

    // Add percentage (optional - you can remove this if you don't want to show it)
    // const percentageText = document.createElement('p');
    // percentageText.style.color = "rgb(255, 255, 255)";
    // percentageText.textContent = `(${percentageScore.toFixed(0)}%)`;
    // modal.appendChild(percentageText);

    const okButton = document.createElement('button');
    okButton.textContent = 'Continue';
    okButton.fontFamily = 'Arial';
    okButton.margin = '15px';
    okButton.style.fontSize = '1em';
    okButton.style.marginTop = '10px';
    okButton.style.padding = '10px 20px';
    okButton.style.borderRadius = '5px';
    okButton.style.border = 'none';
    okButton.style.backgroundColor = '#007BFF';
    okButton.style.color = 'white';
    okButton.style.cursor = 'pointer';
    okButton.addEventListener('click', () => {
        document.body.removeChild(modal);
        document.body.removeChild(overlay);
        window.location.href = "mainpage.html";
    });
    modal.appendChild(okButton);

    const previewButton = document.createElement('button');
    previewButton.textContent = 'Preview';
    previewButton.fontFamily = 'Arial';
    previewButton.style.margin = '15px';
    previewButton.style.fontSize = '1em';
    previewButton.style.marginTop = '20px';
    previewButton.style.padding = '10px 20px';
    previewButton.style.borderRadius = '5px';
    previewButton.style.border = 'none';
    previewButton.style.backgroundColor = '#007BFF';
    previewButton.style.color = 'white';
    previewButton.style.cursor = 'pointer';
    previewButton.addEventListener('click', () => {
        document.body.removeChild(modal);
        document.body.removeChild(overlay);
        showReviewResults();
    });
    modal.appendChild(previewButton);

    // Append the modal to the body
    document.body.appendChild(modal);

    // Disable all other elements on the page
    const allElements = document.querySelectorAll('body > *:not(div)');
    allElements.forEach(element => {
        element.style.pointerEvents = 'none';
        element.style.filter = 'blur(2px)';
    });
}


function showReviewResults() {
    // Create the overlay element
    const overlay = document.createElement('div');
    overlay.classList.add('overlay');
    overlay.style.position = 'fixed';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.width = '100%';
    overlay.style.height = '100%';
    overlay.style.backgroundColor = 'rgba(0,0,0,0.5)';
    overlay.style.zIndex = '1000';
    document.body.appendChild(overlay);

    // Create the modal element
    const modal = document.createElement('div');
    modal.classList.add('modal');
    modal.style.position = 'fixed';
    modal.style.top = '50%';
    modal.style.left = '50%';
    modal.style.transform = 'translate(-50%, -50%)';
    modal.style.textAlign = 'center';
    modal.style.backgroundColor = "#0b0b23";
    modal.style.color = "rgb(255, 255, 255)";
    modal.style.padding = "20px";
    modal.style.boxShadow = "0 0 4px rgba(203, 193, 193, 0.48)";
    modal.style.maxWidth = "800px";
    modal.style.width = "90%";
    modal.style.overflowY = "auto";
    modal.style.maxHeight = "90vh";
    modal.style.zIndex = '1001';
    modal.style.borderRadius = '10px';

    // Add title
    const title = document.createElement('h2');
    title.textContent = 'Review Your Answers';
    title.style.color = "gold";
    title.style.marginBottom = "20px";
    modal.appendChild(title);

    // Create a container for all review items
    const reviewContainer = document.createElement('div');
    reviewContainer.style.textAlign = 'left';

    try {
        // Loop through each box and create a review item
        for (let i = 0; i <= 6; i++) {
            const box = document.getElementById(`box${i}`);
            if (!box) {
                console.error(`Box with ID box${i} not found`);
                continue;
            }

            const image = box.querySelector('img');
            const isCorrect = image && image.id === `img${i}`;

            // Create review item container
            const reviewItem = document.createElement('div');
            reviewItem.style.marginBottom = '15px';
            reviewItem.style.padding = '10px';
            reviewItem.style.border = `2px solid ${isCorrect ? '#28a745' : '#dc3545'}`;
            reviewItem.style.borderRadius = '5px';
            reviewItem.style.backgroundColor = '#1a1a2e';

            // Get description
            const descriptions = document.querySelectorAll('.description');
            if (!descriptions[i]) {
                console.error(`Description ${i} not found`);
                continue;
            }
            const description = descriptions[i].textContent;
            
            const questionText = document.createElement('p');
            questionText.textContent = `${i + 1}. ${description}`;
            questionText.style.fontWeight = 'bold';
            questionText.style.marginBottom = '8px';
            reviewItem.appendChild(questionText);

            // Get correct answer
            const leftItems = document.querySelectorAll('.left-con .item p');
            if (!leftItems[i]) {
                console.error(`Left item ${i} not found`);
                continue;
            }
            const correctAnswer = leftItems[i].textContent;
            
            const correctText = document.createElement('p');
            correctText.innerHTML = `<span style="color: #28a745">✓ Correct Answer:</span> ${correctAnswer}`;
            correctText.style.marginBottom = '5px';
            reviewItem.appendChild(correctText);

            // Add user's answer if wrong
            // if (!isCorrect) {
            //     const userAnswer = image ? 
            //         (image.previousElementSibling ? image.previousElementSibling.textContent : 'Unknown device') : 
            //         'No answer';
            //     const userText = document.createElement('p');
            //     userText.innerHTML = `<span style="color: #dc3545">✗ Your Answer:</span> ${userAnswer}`;
            //     userText.style.marginBottom = '5px';
            //     reviewItem.appendChild(userText);
            // }

            // Add result indicator
            const resultText = document.createElement('p');
            resultText.textContent = isCorrect ? '✓ Correct' : '✗ Incorrect';
            resultText.style.color = isCorrect ? '#28a745' : '#dc3545';
            resultText.style.fontWeight = 'bold';
            reviewItem.appendChild(resultText);

            reviewContainer.appendChild(reviewItem);
        }

        modal.appendChild(reviewContainer);

        // Add close button
        const closeButton = document.createElement('button');
        closeButton.textContent = 'Close';
        closeButton.style.marginTop = '20px';
        closeButton.style.padding = '10px 20px';
        closeButton.style.borderRadius = '5px';
        closeButton.style.border = 'none';
        closeButton.style.backgroundColor = '#007BFF';
        closeButton.style.color = 'white';
        closeButton.style.cursor = 'pointer';
        closeButton.addEventListener('click', () => {
            document.body.removeChild(modal);
            document.body.removeChild(overlay);
            window.location.href = "mainpage.html";
        });
        modal.appendChild(closeButton);

        // Append the modal to the body
        document.body.appendChild(modal);

    } catch (error) {
        console.error('Error in showReviewResults:', error);
        // Show error message if something goes wrong
        const errorMsg = document.createElement('p');
        errorMsg.textContent = 'An error occurred while loading review results.';
        errorMsg.style.color = 'red';
        modal.appendChild(errorMsg);
        
        const okBtn = document.createElement('button');
        okBtn.textContent = 'OK';
        okBtn.onclick = () => {
            document.body.removeChild(modal);
            document.body.removeChild(overlay);
        };
        modal.appendChild(okBtn);
        
        document.body.appendChild(modal);
    }

    // Disable all other elements on the page
    const allElements = document.querySelectorAll('body > *:not(div.overlay):not(div.modal)');
    allElements.forEach(element => {
        element.style.pointerEvents = 'none';
        element.style.filter = 'blur(2px)';
    });
}

// Add an event listener to the submit button
const submitButton = document.querySelector('.btn-submit');
submitButton.addEventListener('click', checkAnswersAndUpdateScore);