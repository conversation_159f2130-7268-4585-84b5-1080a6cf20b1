// Game State
let currentQuestionIndex = 0;
let wrongAnswers = 0;
let timer;
let timeLeft = 30;
let selectedAnswer = null;
let gameActive = false;

// DOM Elements
const questionElement = document.getElementById('question');
const optionsElement = document.getElementById('options');
const nextButton = document.getElementById('next-btn');
const timeElement = document.getElementById('time');
const livesElement = document.getElementById('lives');
const robotElement = document.getElementById('robot');
const gameArea = document.getElementById('game-area');
const instructionModal = document.getElementById('instruction-modal');
const startButton = document.getElementById('start-btn');
const resultsModal = document.getElementById('results-modal');
const resultTitle = document.getElementById('result-title');
const resultStars = document.getElementById('result-stars');
const resultMessage = document.getElementById('result-message');
const restartButton = document.getElementById('restart-btn');
const volumeSlider = document.getElementById('volume-slider');
const volumeValue = document.getElementById('volume-value');

// Sound Effects
const correctSound = new Audio('/sounds/correct.mp3');
const wrongSound = new Audio('/sounds/wrong.mp3');
const breakSound = new Audio('/sounds/break.mp3');
const successSound = new Audio('/sounds/success.mp3');

// Set initial volume for all sound effects
correctSound.volume = 0.3;
wrongSound.volume = 0.3;
breakSound.volume = 0.3;
successSound.volume = 0.3;

// Initialize Game
function initGame() {
    currentQuestionIndex = 0;
    wrongAnswers = 0;
    gameActive = true;
    livesElement.textContent = 5 - wrongAnswers;
    resetRobotPosition();
    resetState();
    showQuestion();
    startTimer();
}

// Show Current Question
function showQuestion() {
    if (currentQuestionIndex >= questions.length) {
        endGame(true);
        return;
    }

    resetState();
    const currentQuestion = questions[currentQuestionIndex];
    questionElement.textContent = currentQuestion.question;

    currentQuestion.options.forEach((option, index) => {
        const button = document.createElement('button');
        button.className = 'option';
        button.textContent = option;
        button.addEventListener('click', () => selectAnswer(index));
        optionsElement.appendChild(button);
    });
}

// Reset Question State
function resetState() {
    selectedAnswer = null;
    timeLeft = 30;
    timeElement.textContent = timeLeft;
    nextButton.disabled = true;
    optionsElement.innerHTML = '';
}

// Select Answer
function selectAnswer(index) {
    if (!gameActive || selectedAnswer !== null) return;

    selectedAnswer = index;
    const options = document.querySelectorAll('.option');
    
    // Highlight selected answer
    options.forEach((option, i) => {
        if (i === index) {
            option.classList.add('selected');
        }
        option.disabled = true;
    });
    
    nextButton.disabled = false;
    clearInterval(timer);

    const currentQuestion = questions[currentQuestionIndex];
    if (index !== currentQuestion.correctAnswer) {
        wrongAnswers++;
        livesElement.textContent = 5 - wrongAnswers;
        lowerRobot();
        
        // Play wrong answer sound
        playSound(wrongSound);
        
        // Check if game is over due to too many wrong answers
        if (wrongAnswers >= 5) {
            // The makeRobotDie function will call endGame(false)
            makeRobotDie();
        }
    } else {
        // Play correct answer sound
        playSound(correctSound);
    }
}

// Lower Robot
function lowerRobot() {
    const currentTop = parseInt(robotElement.style.top || '70');
    const newTop = currentTop + 40;
    
    // Get elements
    const ropeElement = document.querySelector('.rope');
    const ropeAroundNeck = document.querySelector('.rope-around-neck');
    
    // Calculate new rope length based on robot position
    const ropeLength = 40 + (newTop - 70);
    
    // Apply smooth transitions
    robotElement.style.transition = 'top 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
    ropeElement.style.transition = 'height 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
    ropeAroundNeck.style.transition = 'transform 0.3s ease-in-out';
    
    // Set new positions
    robotElement.style.top = newTop + 'px';
    ropeElement.style.height = ropeLength + 'px';
    
    // Add a slight swing effect to the robot
    robotElement.style.animation = 'none';
    setTimeout(() => {
        robotElement.style.animation = 'sway 2s ease-in-out infinite';
    }, 800); // Wait for the transition to complete
    
    // Add a visual effect to the rope-around-neck
    ropeAroundNeck.style.transform = 'translateX(-50%) scale(1.1)';
    setTimeout(() => {
        ropeAroundNeck.style.transform = 'translateX(-50%) scale(1)';
    }, 300);

    if (wrongAnswers >= 5) {
        makeRobotDie();
    }
}

// Make Robot Die Dramatically
function makeRobotDie() {
    // Disable game controls
    gameActive = false;
    clearInterval(timer);
    
    // Add dramatic dying animation
    robotElement.style.animation = 'none';
    robotElement.style.transition = 'all 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
    
    // First, make the robot swing back and forth
    robotElement.style.transform = 'rotate(-15deg)';
    
    // Change robot's appearance to look "dead"
    const eyes = document.querySelectorAll('.eye');
    eyes.forEach(eye => {
        eye.style.background = 'linear-gradient(145deg, #e0e0e0, #bdbdbd)';
        const pupil = eye.querySelector('.pupil');
        if (pupil) {
            pupil.style.height = '2px';
            pupil.style.top = '8px';
            pupil.style.transform = 'rotate(90deg)';
        }
    });
    
    // Change mouth to look sad
    const mouth = document.querySelector('.mouth');
    if (mouth) {
        mouth.style.height = '5px';
        mouth.style.width = '20px';
        mouth.style.borderRadius = '0 0 10px 10px';
        mouth.style.background = 'linear-gradient(to bottom, #b71c1c, #7f0000)';
    }
    
    // Turn off the antenna light
    const antenna = document.querySelector('.antenna::after');
    if (antenna) {
        antenna.style.background = '#9e9e9e';
        antenna.style.boxShadow = 'none';
    }
    
    // Turn off the buttons
    const buttons = document.querySelectorAll('.button');
    buttons.forEach(button => {
        button.style.background = '#9e9e9e';
        button.style.boxShadow = 'none';
        button.style.animation = 'none';
    });
    
    setTimeout(() => {
        robotElement.style.transform = 'rotate(15deg)';
        
        // After swinging, make the robot fall
        setTimeout(() => {
            // Break the rope effect
            const ropeElement = document.querySelector('.rope');
            ropeElement.style.height = '400px';
            ropeElement.style.opacity = '0.5';
            
            // Make the robot fall
            robotElement.style.top = '350px';
            robotElement.style.transform = 'rotate(90deg)';
            
            // Add a visual effect to the rope-around-neck
            const ropeAroundNeck = document.querySelector('.rope-around-neck');
            ropeAroundNeck.style.transform = 'translateX(-50%) scale(1.2)';
            
            // Add a "breaking" sound effect
            playSound(breakSound);
            
            // Show game over after the fall
            setTimeout(() => {
                endGame(false);
            }, 1000);
        }, 500);
    }, 300);
}

// Play Sound Effect
function playSound(sound) {
    // Reset the sound to the beginning
    sound.currentTime = 0;
    // Play the sound
    sound.play().catch(e => console.log('Audio play failed:', e));
}

// Reset Robot Position
function resetRobotPosition() {
    robotElement.style.top = '70px';
    robotElement.style.transform = 'rotate(0deg)';
    
    // Reset rope length
    const ropeElement = document.querySelector('.rope');
    ropeElement.style.height = '40px';
    ropeElement.style.opacity = '1';
    
    // Reset rope-around-neck
    const ropeAroundNeck = document.querySelector('.rope-around-neck');
    ropeAroundNeck.style.transform = 'translateX(-50%) scale(1)';
    
    // Reset robot appearance
    resetRobotAppearance();
    
    // Reset animation
    robotElement.style.animation = 'sway 4s ease-in-out infinite';
}

// Reset Robot Appearance
function resetRobotAppearance() {
    // Reset eyes
    const eyes = document.querySelectorAll('.eye');
    eyes.forEach(eye => {
        eye.style.background = 'linear-gradient(145deg, #ffffff, #e6e6e6)';
        const pupil = eye.querySelector('.pupil');
        if (pupil) {
            pupil.style.height = '8px';
            pupil.style.top = '4px';
            pupil.style.transform = 'none';
        }
    });
    
    // Reset mouth
    const mouth = document.querySelector('.mouth');
    if (mouth) {
        mouth.style.height = '10px';
        mouth.style.width = '30px';
        mouth.style.borderRadius = '5px';
        mouth.style.background = 'linear-gradient(to bottom, #e53935, #c62828)';
    }
    
    // Reset antenna light
    const antenna = document.querySelector('.antenna::after');
    if (antenna) {
        antenna.style.background = '#f44336';
        antenna.style.boxShadow = '0 0 10px #ff6659';
    }
    
    // Reset buttons
    const buttons = document.querySelectorAll('.button');
    buttons.forEach((button, index) => {
        button.style.background = index === 0 ? '#f44336' : (index === 1 ? '#ffeb3b' : '#4caf50');
        button.style.boxShadow = 'inset 0 -2px 3px rgba(0, 0, 0, 0.3), 0 2px 3px rgba(0, 0, 0, 0.3)';
        button.style.animation = 'glow 2s infinite alternate';
        button.style.animationDelay = `${index * 0.5}s`;
    });
}

// Next Question
function nextQuestion() {
    currentQuestionIndex++;
    showQuestion();
    startTimer();
}

// Timer
function startTimer() {
    clearInterval(timer);
    timeLeft = 15;
    timeElement.textContent = timeLeft;

    timer = setInterval(() => {
        timeLeft--;
        timeElement.textContent = timeLeft;

        if (timeLeft <= 0) {
            clearInterval(timer);
            if (selectedAnswer === null) {
                wrongAnswers++;
                livesElement.textContent = 5 - wrongAnswers;
                lowerRobot();
                document.querySelectorAll('.option').forEach(opt => opt.disabled = true);
                nextButton.disabled = false;
                
                // Check if game is over due to too many wrong answers
                if (wrongAnswers >= 5) {
                    // The makeRobotDie function will call endGame(false)
                    makeRobotDie();
                }
            }
        }
    }, 1000);
}

// End Game
function endGame(success) {
    gameActive = false;
    clearInterval(timer);

    if (!success) {
        // Robot has already died in makeRobotDie function
        resultTitle.textContent = 'Mission Failed!';
    } else {
        resultTitle.textContent = 'Mission Complete!';
        
        // Add a celebration animation
        robotElement.style.animation = 'celebrate 1s ease-in-out infinite';
        
        // Play success sound
        playSound(successSound);
    }

    showResults(success);
}

// Show Results with Star Rating
function showResults(success) {
    const totalQuestions = questions.length;
    const correctAnswers = totalQuestions - wrongAnswers;
    const percentage = (correctAnswers / totalQuestions) * 100;

    // Calculate stars (1-5)
    let stars;
    if (!success) {
        stars = 0;
    } else if (percentage >= 90) {
        stars = 5;
    } else if (percentage >= 70) {
        stars = 4;
    } else if (percentage >= 50) {
        stars = 3;
    } else if (percentage >= 30) {
        stars = 2;
    } else {
        stars = 1;
    }

    // Display stars
    resultStars.innerHTML = '';
    for (let i = 0; i < 5; i++) {
        const star = document.createElement('span');
        star.textContent = i < stars ? '★' : '☆';
        star.style.color = i < stars ? '#ffeb3b' : '#555';
        resultStars.appendChild(star);
    }

    // Set message
    if (!success) {
        resultMessage.textContent = 'The robot couldn\'t be saved!';
    } else {
        resultMessage.textContent = `You answered ${correctAnswers} out of ${totalQuestions} questions correctly and saved the robot!`;
    }

    resultsModal.style.display = 'flex';
}

// Event Listeners
startButton.addEventListener('click', () => {
    instructionModal.style.display = 'none';
    gameArea.style.display = 'flex';
    initGame();
});

nextButton.addEventListener('click', nextQuestion);

restartButton.addEventListener('click', () => {
    resultsModal.style.display = 'none';
    pullRobotBack();
});

// Volume Control Event Listener
volumeSlider.addEventListener('input', () => {
    const volume = volumeSlider.value / 100;
    volumeValue.textContent = `${volumeSlider.value}%`;
    
    // Update all sound volumes
    correctSound.volume = volume;
    wrongSound.volume = volume;
    breakSound.volume = volume;
    successSound.volume = volume;
    
    // Update speaker icon based on volume
    updateSpeakerIcon(volume);
    
    // Play a test sound if volume is not zero
    if (volume > 0) {
        // Create a temporary sound for testing
        const testSound = new Audio('sounds/correct.mp3');
        testSound.volume = volume;
        testSound.play().catch(e => console.log('Test sound play failed:', e));
    }
});

// Add mute/unmute functionality
const volumeLabel = document.querySelector('.volume-control label');
let previousVolume = 30; // Default volume

volumeLabel.addEventListener('click', () => {
    if (volumeSlider.value > 0) {
        // Store current volume and mute
        previousVolume = parseInt(volumeSlider.value);
        volumeSlider.value = 0;
        volumeValue.textContent = '0%';
        
        // Update all sound volumes
        correctSound.volume = 0;
        wrongSound.volume = 0;
        breakSound.volume = 0;
        successSound.volume = 0;
        
        // Update speaker icon
        updateSpeakerIcon(0);
    } else {
        // Restore previous volume
        volumeSlider.value = previousVolume;
        volumeValue.textContent = `${previousVolume}%`;
        
        // Update all sound volumes
        const volume = previousVolume / 100;
        correctSound.volume = volume;
        wrongSound.volume = volume;
        breakSound.volume = volume;
        successSound.volume = volume;
        
        // Update speaker icon
        updateSpeakerIcon(volume);
    }
});

// Function to update speaker icon based on volume
function updateSpeakerIcon(volume) {
    if (volume === 0) {
        volumeLabel.textContent = '🔇';
    } else if (volume < 0.3) {
        volumeLabel.textContent = '🔈';
    } else if (volume < 0.7) {
        volumeLabel.textContent = '🔉';
    } else {
        volumeLabel.textContent = '🔊';
    }
}

// Pull Robot Back Up
function pullRobotBack() {
    // First, make the robot appear to be pulled back up
    const robotElement = document.getElementById('robot');
    const ropeElement = document.querySelector('.rope');
    const ropeAroundNeck = document.querySelector('.rope-around-neck');
    
    // Reset robot appearance first
    resetRobotAppearance();
    
    // Add pulling animation
    robotElement.style.transition = 'all 1.5s cubic-bezier(0.34, 1.56, 0.64, 1)';
    robotElement.style.top = '70px';
    robotElement.style.transform = 'rotate(0deg)';
    
    // Animate the rope shortening
    ropeElement.style.transition = 'all 1.5s cubic-bezier(0.34, 1.56, 0.64, 1)';
    ropeElement.style.height = '40px';
    ropeElement.style.opacity = '1';
    
    // Reset rope-around-neck
    ropeAroundNeck.style.transition = 'all 1.5s cubic-bezier(0.34, 1.56, 0.64, 1)';
    ropeAroundNeck.style.transform = 'translateX(-50%) scale(1)';
    
    // Add a slight bounce effect when the robot reaches the top
    setTimeout(() => {
        robotElement.style.transition = 'all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1)';
        robotElement.style.top = '65px';
        
        setTimeout(() => {
            robotElement.style.top = '70px';
            
            // Start the normal sway animation after the bounce
            setTimeout(() => {
                robotElement.style.animation = 'sway 4s ease-in-out infinite';
                robotElement.style.transition = 'none';
                
                // Initialize the game after the animation completes
                initGame();
            }, 300);
        }, 150);
    }, 1500);
}

// Start with instructions
window.addEventListener('DOMContentLoaded', () => {
    instructionModal.style.display = 'flex';
    gameArea.style.display = 'none';
    resultsModal.style.display = 'none';
    addCelebrationAnimation();
});

// Add this to your CSS or create a new style element
function addCelebrationAnimation() {
    const style = document.createElement('style');
    style.textContent = `
        @keyframes celebrate {
            0%, 100% { transform: rotate(0deg) translateY(0); }
            25% { transform: rotate(-5deg) translateY(-5px); }
            75% { transform: rotate(5deg) translateY(-5px); }
        }
        
        @keyframes fall {
            0% { transform: rotate(0deg); }
            20% { transform: rotate(-15deg); }
            40% { transform: rotate(15deg); }
            60% { transform: rotate(-10deg); }
            80% { transform: rotate(10deg); }
            100% { transform: rotate(90deg); }
        }
        
        @keyframes die {
            0% { transform: rotate(0deg); }
            20% { transform: rotate(-15deg); }
            40% { transform: rotate(15deg); }
            60% { transform: rotate(-10deg); }
            80% { transform: rotate(10deg); }
            100% { transform: rotate(90deg); }
        }
        
        @keyframes pullBack {
            0% { transform: rotate(90deg); }
            100% { transform: rotate(0deg); }
        }
    `;
    document.head.appendChild(style);
}