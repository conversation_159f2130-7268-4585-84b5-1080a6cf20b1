<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>

    <link rel="stylesheet" href="/css/style_game5.css">
    <script src="/js/game5.js" defer></script>
</head>
<body>
    <div class="container">
        <div class="header">
          <!--   <p class="score-lbl">Score: <p class="score-txt">0</p></p> -->
            <a href="mainpage.html">&times;</a>
        </div>

        <div class="box-con">
            <div class="box-con-header">
                <img src="/images/game4/fileIcon_nobg.png" alt="">

                <div class="health-bar" data-total="100" data-value="100">
                    <div class="bar">
                          <div class="hit"></div>
                    </div>
                </div>
            </div>

            <div class="box-con-row">

                <div class="left-con">
                    <div class="left-con-header">
                        <p class="title">Question</p>
                    </div>

                    <div class="left-con-body">
                        <p>Lorem ipsum dolor, sit amet consectetur adipisicing elit. 
                            Doloribus fugiat omnis accusantium voluptates corporis numquam earum eum? Amet, 
                            sed! Excepturi consequatur quidem omnis illo earum ipsa voluptatibus dolorem. Tempora, iusto.</p>
                    </div>
                </div>

                <div class="right-con">
                    <div class="right-con-header">
                        <p class="title">Answer</p>
                    </div>
                    
                    <div class="right-con-body">
                        <div class="options">     
                            <input type="radio" name="option" id="opt0"><label for="opt0">Options</label>
                        </div>
                        <div class="options">
                            <input type="radio" name="option" id="opt1"><label for="opt1">Options</label>
                        </div>
                        <div class="options">
                            <input type="radio" name="option" id="opt2"><label for="opt2">Options</label>
                        <div class="options">        
                            <input type="radio" name="option" id="opt3"><label for="opt3">Options</label>
                        </div>
                    </div>
                </div>

            </div>

            <div class="box-footer">
                <button type="button" id="btn_next">Next</button>
            </div>

        </div>
    </div>

</body>
</html>