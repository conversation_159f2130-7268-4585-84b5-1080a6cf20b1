@import url('https://fonts.googleapis.com/css2?family=Cinzel:wght@400;700&display=swap');

body{   
    background-color: rgb(11, 8, 16);
}   

p{
    margin: 0px;
}

/* HEADER AND TITLE DESIGN */
.header{
    background-color: transparent;
    border-radius: 10px;
    display: flex;
    min-width: 100%;
    min-height: 20vh;
    margin-top: 2%;
}   

.header .title{
    padding-top: 10px;
    padding-left: 8%;
}

.body{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    width: 100%;
    height: 50vh;

}

.title{
    font-size: 4em;
    font-family: "Cinzel", "sans-serif";
    color: white;
    margin-bottom: 40px;
    text-shadow: 0px 0px 5px rgb(255, 255, 255), 0px 0px 15px rgb(255, 255, 255);
}

.title a{
    text-decoration: none;
    color: white;
}

/* LOGIN AND SIGNUP DESIGN */
.login-cont, .signup-cont{
    border: 1px solid white;
    border-radius: 24px;
    padding: 10px;
    color: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    animation: bottomToTop .8s 1 ease-in-out;
}

@keyframes bottomToTop {
    from {
        opacity: 0;
        transform: translateY(10%);
    }
    to {
        opacity: 1;
        transform: translateY(0%);
    }
}

.login-cont .login-title, .signup-cont .signup-title{
    font-size: 2em;
    font-family: "Cinzel", "sans-serif";
    margin: 10px;
    padding-bottom: 20px;
}

.login-cont form, .signup-cont form{
    display: flex;
    flex-direction: column;
}

.login-cont form label, .signup-cont form label{
    font-size: 1em;
    font-family: "Cinzel", "sans-serif";
    margin-left: 10px;
    color: white;   
}

.login-cont form input, .signup-cont form input{
    margin: 10px;
    padding: 10px 10px  ;
    font-size: 1.2em;
    font-family: "Cinzel", "sans-serif";
    border-radius: 5px;
    border: 1px solid white;
    background-color: transparent;
    width: 400px;
    color: white;
}

.btn-cont{
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    margin-bottom: 20px;
}

.btn-cont button{
    padding: 10px;
    font-size: .9em;
    font-family: "Cinzel", "sans-serif";
    border-radius: 5px;
    padding: 5px 15px;
    border: 0px;
    background-color: rgb(0, 7, 138);
    color: white;
    cursor: pointer;
}

.btn-cont a{
    padding: 10px;
    font-size: .9em;
    font-family: "Cinzel", "sans-serif";
    padding: 5px 15px;
    color: white;
    text-decoration: underline;
}