@import url('https://fonts.googleapis.com/css2?family=Cinzel:wght@400;700&display=swap');

body{   
    background-color: rgb(11, 8, 16);
    animation: visibility .5s 1;
}   

@keyframes visibility{
    0%{
        opacity: 0;

    }
    100%{
        opacity: 1;

    }
    
}

p{
    margin: 0px;
}

.header{

    display: flex;
    justify-content: space-between;
    min-width: 100%;
    min-height: 15vh;
}

.box-cont1{
    margin-left: 1.5%;
    display: flex;
    align-items: flex-end;
}

.box-cont1 p{
    color: white;
    font-size: 2.5em;
    font-family: "Cinzel", "sans-serif";
}

.box-cont2{
    margin-right: 1.5%;
    max-height: 100px;
    display: flex;
    align-items: flex-end;
    justify-content: center;
}

.box-cont2-bin{
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    margin: 0px 10px;
    width: fit-content;
    height: auto;
}

.profile-icon{
    filter: invert(100%);
}

    .toggle-profile {
    height: 100%;
    width: 0;
    position: fixed;
    z-index: 1;
    top: 0;
    right: 0;
    background-color: #111;
    overflow-x: hidden;
    transition: 0.5s;
    padding-top: 60px;
    text-align: center;
    font-family: Arial, Helvetica, sans-serif;
    }

    .box-cont-exp{
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
    }

    .box-cont-exp .user_exp {
        color: white;
        font-size: 2.5em;
        font-family: "Cinzel", "sans-serif";
        margin: 0px 0px 0px 0px;
    }

    .box-cont-exp .exp_label{
        color: white;
        font-size: 1em;
        font-family: "Cinzel", "sans-serif";
        margin: 0px 0px 0px 0px;
    }

    .toggle-profile a, .toggle-profile p{
    margin: 20px 0px;
    text-decoration: none;
    font-size: 25px;
    color: #818181;
    display: block;
    transition: 0.3s;
    }   

    .toggle-profile p{
        font-size: 18px;
    }

    .toggle-profile img{
    width: 100px;
    filter: invert(85%);
    }

    .toggle-profile a:hover {
    color: #f1f1f1;
    }

    .toggle-profile .closebtn {
    position: absolute;
    top: 0;
    right: 25px;
    font-size: 40px;
    margin-left: 50px;
    }  

.box-cont2-bin:hover{
    transform: scale(1.1);
    transition: 0.5s;
}

.box-cont2-bin p{
    color: white;
    font-size: .8em;
    font-family: "Cinzel", "sans-serif";
}

.box-cont2-bin img{
    width: auto;
    object-fit: contain;
    height: 50px;

}


.body{
    display: flex;
    justify-content: center;
    align-items: flex-start;

    min-height: 55vh;
    height: auto;
}

.cont-levels{
    margin: 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
}

.cont-levels-row{
    display: grid;
    width: 50%;
    grid-template-columns: repeat(auto-fill, 120px);
    gap: 20px;
    justify-content: center;
}

.cont-levels-column a{
    text-decoration: none;
    color: white;
}

.cont-levels-column button{
    background-color: rgb(15, 0, 227);
    color: white;
    font-size: 3em;
    font-family: "Cinzel", "sans-serif";
    width: 120px;
    height: 120px;
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

/* Style for locked levels */
.cont-levels-column button.locked {
    background-color: #818181;
    opacity: 0.7;
    cursor: not-allowed;
}

.locked-level {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.locked-level img {
    width: 50%;
    height: 50%;
    margin-bottom: 5px;
    filter: grayscale(100%);
}

.locked-level span {
    font-size: 1.2em;
}

/* Style for locked levels */