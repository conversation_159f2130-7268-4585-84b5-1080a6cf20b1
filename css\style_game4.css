*{
    margin: 0;
    padding: 0;

    font-family: Arial, Helvetica, sans-serif;
}

body{
    background-color: rgb(11, 11, 35);
}

.container{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    padding-bottom: 10px;
    
}

.box-con{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    width: 98vw;
    box-shadow: 0px 0px 5px rgba(236, 231, 231, 0.397);
    padding: 10px;
    min-height: 50vh;
}

.box-con-row{
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 20px;
}

.box-con-row .title{
    font-size: 2em;
    color: white;
    margin: 0px;
}

.header{
    width: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;

}

.header a{
    font-size: 50px;
    text-decoration: none;
    padding: 0px 10px;
    margin: 0;
    color: white;
}

.box-con-header{
    width: 80%;
    margin: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.box-con-header img{
    width: 100px;
    height: 100%;
    margin-right: 10px;
}

.health-bar {
    width: 100%;
    height: 20px;
    background: #eee;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 20px;
}

.bar {
    height: 100%;
    width: 100%;
    background: #ddd;
}

.hit {
    height: 100%;
    width: 100%;
    background: #4CAF50;
    transition: all 0.3s ease;
}

.left-con, .right-con{
    padding: 10px;
    min-width: 500px;
    width: 50%;
    color: white;
}

.right-con{
    width: 40%;
}

.left-con-body, .right-con-body{

    margin: 10px;
    font-family: Arial, Helvetica, sans-serif;
    font-size: 1.5em;
    min-height: 25vh;
}

.left-con-body{
    display: flex;
    justify-content: center;
    align-items: center;
    max-width: 80%;
}

.right-con-body{
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    align-items: flex-start;
    justify-content: center;
}

.options input[type='radio'] {
    width: 20px;
    border: 2px solid #1a7de8;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    cursor: pointer;
    margin-right: 10px;
}

.options input[type='radio']:checked {
    background: #1a7de8;    
}

.box-footer{

    width: 90%;
    display: flex;
    justify-content: flex-end;
}

.box-footer button{
    padding: 7px 10px;
    border-radius: 10%;
    font-size: 1.3em;
    background-color: rgb(20, 189, 54);
    color: white;
    border: none;
    cursor: pointer;
}

/* Overlay styles */
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    animation: fadeIn 0.3s ease-in-out;
}

/* Modal styles */
.modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    text-align: center;
    animation: scaleUp 0.3s ease-in-out;
}

/* Fade-in animation for overlay */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Scale-up animation for modal */
@keyframes scaleUp {
    from {
        transform: translate(-50%, -50%) scale(0.8);
        opacity: 0;
    }
    to {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
}