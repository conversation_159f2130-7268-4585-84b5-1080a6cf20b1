<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>

    <link rel="stylesheet" href="/css/style_game3.css">
    <script src="/js/game3.js" defer></script>
</head>
<body>
    <div class="container">
        <div class="header">
          <!--   <p class="score-lbl">Score: <p class="score-txt">0</p></p> -->
            <a href="mainpage.html">&times;</a>
        </div>

        <div class="box-con">

            <div class="left-con">
                <div class="item">
                    <p>Bus</p>
                    <img class="img" id="img0" src="/images/game3/bus.png" alt="BUS" srcset="">
                </div>

                <div class="item">
                    <p>Star</p>
                    <img class="img" id="img1"  src="/images/game3/star.png" alt="STAR" srcset="">
                </div>

                <div class="item">
                    <p>Ring</p>
                    <img class="img" id="img2"  src="/images/game3/ring.png" alt="RING" srcset="">
                </div>

                <div class="item">
                    <p>Mesh</p>
                    <img class="img" id="img3"  src="/images/game3/mesh.png" alt="MESH" srcset="">
                </div>

                <div class="item">
                    <p>Hybrid</p>
                    <img class="img" id="img4"  src="/images/game3/hybrid.png" alt="HYBRID" srcset="">
                </div>

                <div class="item">
                    <p>Tree</p>
                    <img class="img" id="img5"  src="/images/game3/tree.png" alt="TREE" srcset="">
                </div>

                <div class="item">
                    <p>Point to point</p>
                    <img class="img" id="img6"  src="/images/game3/ptp.png" alt="POINT TO POINT" srcset="">
                </div>

            </div>

            <div class="right-con">
                <div class="right-con-bin">
                    <div class="item-bin">
                        <span class="image-con droppable" id=box0>

                        </span>
                    </div>

                    <div class="description-bin">
                        <p class="description">All devices share a single communication line (bus). If the main cable fails, the whole network is affected, but it's cost-effective.</p>
                    </div>
                </div>

                <div class="right-con-bin">
                    <div class="item-bin">
                        <span class="image-con droppable" id="box1">

                        </span>
                    </div>

                    <div class="description-bin">
                        <p class="description">All devices are connected to a single central hub or switch. If the hub fails, the entire network goes down, but troubleshooting is easy.</p>
                    </div>
                </div>

                <div class="right-con-bin">
                    <div class="item-bin">
                        <span class="image-con droppable" id="box2">

                        </span>
                    </div>

                    <div class="description-bin">
                        <p class="description">Devices are connected in a closed loop, where data travels in one direction. A single break can disrupt the entire network.</p>
                    </div>
                </div>

                <div class="right-con-bin">
                    <div class="item-bin">
                        <span class="image-con droppable" id="box3">

                        </span>
                    </div>

                    <div class="description-bin">
                        <p class="description">Every device is connected to every other device, providing high redundancy but requiring many cables, making it expensive.</p>
                    </div>
                </div>

                <div class="right-con-bin">
                    <div class="item-bin">
                        <span class="image-con droppable" id="box4">

                        </span>
                    </div>

                    <div class="description-bin">
                        <p class="description">A combination of two or more different topologies (e.g., Star-Bus or Star-Ring), offering flexibility but complexity in design.</p>
                    </div>
                </div>

                <div class="right-con-bin">
                    <div class="item-bin">
                        <span class="image-con droppable" id="box5">

                        </span>
                    </div>

                    <div class="description-bin">
                        <p class="description">A hierarchical structure where nodes are connected like branches of a tree. It's scalable but relies heavily on the root node.</p>
                    </div>
                </div>

                <div class="right-con-bin">
                    <div class="item-bin">
                        <span class="image-con droppable" id="box6">

                        </span>
                    </div>

                    <div class="description-bin">
                        <p class="description"> A direct connection between two devices, commonly used in simple setups like connecting a computer to a printer.</p>
                    </div>
                </div>

            </div>

            <button class="btn-submit" type="button">Submit</button>
        </div>
    </div>

</body>
</html>