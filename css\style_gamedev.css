* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: Arial, Helvetica, sans-serif;
}

body {
    background-color: rgb(11, 11, 35);
    color: white;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.quiz-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    margin-bottom: 20px;
}

.quiz-title {
    text-align: center;
    margin-bottom: 30px;
}

.quiz-title h1 {
    font-size: 2.2rem;
    color: #fff;
}

.quiz-content {
    display: flex;
    gap: 30px;
    flex: 1;
}

.question-container {
    flex: 1;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 20px;
}

.answers-container {
    flex: 1;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 20px;
}

.question-body p {
    font-size: 1.5rem;
    line-height: 1.5;
}

.answers-body {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.option {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.option:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.option input[type='radio'] {
    appearance: none;
    width: 20px;
    height: 20px;
    border: 2px solid #1a7de8;
    border-radius: 50%;
    cursor: pointer;
}

.option input[type='radio']:checked {
    background: #1a7de8;    
}

.quiz-footer {
    display: flex;
    justify-content: flex-end;
    padding: 20px 0;
}

#btn_next {
    padding: 12px 25px;
    border-radius: 5px;
    font-size: 1.1rem;
    background-color: rgb(20, 189, 54);
    color: white;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s;
}

#btn_next:hover {
    background-color: rgb(16, 160, 45);
}

/* Timer styles */
.timer-container {
    display: flex;
    align-items: center;
    font-size: 1.3rem;
    gap: 10px;
}

.timer-label {
    color: #ccc;
}

.timer {
    font-weight: bold;
    color: #ffcc00;
    min-width: 50px;
    text-align: center;
}

/* Robot Hangman Styles - Always Visible */
/* Enhanced Robot Hangman Styles */
.hanged-robot {
    position: relative;
    width: 180px;
    height: 300px;
    margin-right: 20px;
}

/* Gallows with metal details */
.robot-gallows {
    position: absolute;
    width: 140px;
    height: 12px;
    background: linear-gradient(to right, #8B4513, #A0522D, #8B4513);
    top: 0;
    left: 20px;
    border-radius: 3px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.3);
}

.robot-gallows::before {
    content: '';
    position: absolute;
    width: 12px;
    height: 60px;
    background: linear-gradient(to bottom, #8B4513, #A0522D);
    top: 0;
    left: 0;
    border-radius: 3px;
}

.robot-gallows::after {
    content: '';
    position: absolute;
    width: 12px;
    height: 300px;
    background: linear-gradient(to bottom, #8B4513, #A0522D);
    top: 0;
    right: 0;
    border-radius: 3px;
}

/* Rope */
.rope {
    position: absolute;
    width: 6px;
    height: 50px;
    background: linear-gradient(to right, #8B8B8B, #D3D3D3, #8B8B8B);
    top: 12px;
    left: 87px;
    z-index: 10;
    border-radius: 3px;
}

.rope::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    border: 3px solid #D3D3D3;
    border-radius: 50%;
    bottom: -10px;
    left: -7px;
}

/* Robot Head with Rope */
.robot-head {
    width: 80px;
    height: 80px;
    background: linear-gradient(145deg, #1E88E5, #0D47A1);
    border-radius: 50% 50% 40% 40%;
    top: 62px;
    left: 50px;
    z-index: 2;
    box-shadow: 
        inset 0 0 15px rgba(0,0,0,0.3),
        0 0 10px rgba(30, 136, 229, 0.6);
    border: 2px solid #0D47A1;
}

/* Robot Face */
.robot-face {
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 3;
}

.robot-eye {
    width: 15px;
    height: 15px;
    background-color: #FFD600;
    border-radius: 50%;
    top: 25px;
    box-shadow: 
        0 0 10px #FFD600,
        inset 0 0 5px #FF6D00;
}

.left-eye { left: 20px; }
.right-eye { right: 20px; }

.robot-mouth {
    width: 40px;
    height: 8px;
    background-color: #FF6D00;
    border-radius: 0 0 20px 20px;
    bottom: 20px;
    left: 20px;
    box-shadow: 0 0 8px #FF6D00;
}

/* Robot Antennas */
.robot-antenna {
    width: 4px;
    height: 20px;
    background: linear-gradient(to bottom, #42A5F5, #0D47A1);
    top: -20px;
    z-index: 1;
}

.left-antenna {
    left: 20px;
    transform: rotate(-15deg);
}

.right-antenna {
    right: 20px;
    transform: rotate(15deg);
}

.robot-antenna::after {
    content: '';
    position: absolute;
    width: 8px;
    height: 8px;
    background-color: #FFD600;
    border-radius: 50%;
    bottom: -4px;
    left: -2px;
    box-shadow: 0 0 5px #FFD600;
}

/* Robot Body with Panels */
.robot-body {
    width: 70px;
    height: 100px;
    background: linear-gradient(145deg, #0D47A1, #1E88E5);
    top: 140px;
    left: 55px;
    z-index: 1;
    border-radius: 10px;
    box-shadow: 
        inset 0 0 20px rgba(0,0,0,0.3),
        0 0 10px rgba(30, 136, 229, 0.6);
    border: 2px solid #1E88E5;
}

.robot-panel {
    background-color: rgba(0,0,0,0.4);
    border: 1px solid #42A5F5;
    border-radius: 3px;
}

.robot-panel.large-panel {
    width: 50px;
    height: 30px;
    top: 20px;
    left: 10px;
}

.robot-panel.small-panel {
    width: 20px;
    height: 15px;
    top: 60px;
    left: 25px;
}

/* Robot Limbs with Joints */
.robot-left-arm, 
.robot-right-arm {
    width: 12px;
    height: 80px;
    background: linear-gradient(to bottom, #42A5F5, #0D47A1);
    top: 140px;
    z-index: 0;
    border-radius: 5px;
}

.robot-left-arm { left: 35px; transform: rotate(30deg); }
.robot-right-arm { left: 113px; transform: rotate(-30deg); }

.robot-left-leg, 
.robot-right-leg {
    width: 15px;
    height: 90px;
    background: linear-gradient(to bottom, #1565C0, #0D47A1);
    top: 230px;
    z-index: 0;
    border-radius: 0 0 5px 5px;
}

.robot-left-leg { left: 55px; transform: rotate(10deg); }
.robot-right-leg { left: 95px; transform: rotate(-10deg); }

.robot-joint {
    width: 18px;
    height: 8px;
    background-color: #FFD600;
    border-radius: 4px;
    position: absolute;
}

.robot-joint::before {
    content: '';
    position: absolute;
    width: 4px;
    height: 15px;
    background-color: #FFD600;
    top: -15px;
    left: 7px;
}

/* Arm joints */
.robot-left-arm .robot-joint,
.robot-right-arm .robot-joint {
    top: 30px;
    left: -3px;
}

/* Leg joints */
.robot-left-leg .robot-joint,
.robot-right-leg .robot-joint {
    top: 0;
    left: -1px;
}

/* Activation States */
.inactive-part {
    opacity: 1  ;
    filter: grayscale(70%) brightness(0.7);
}

.active-part {
    opacity: 1;
    filter: grayscale(0%) brightness(1);
    animation: activatePart 0.5s ease-in-out;
}

@keyframes activatePart {
    0% { 
        transform: scale(0.8); 
        opacity: 0.3;
    }
    50% { 
        transform: scale(1.1); 
        box-shadow: 0 0 20px rgba(66, 165, 245, 0.8);
    }
    100% { 
        transform: scale(1); 
        opacity: 1;
    }
}

/* Modal styles */
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
}

.modal {
    background-color: rgb(11, 11, 35);
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
    max-width: 500px;
    width: 90%;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.modal h2 {
    color: #fff;
    margin-bottom: 1.5rem;
    font-size: 1.8rem;
}

.modal p {
    color: #ccc;
    margin-bottom: 1.5rem;
    font-size: 1.2rem;
    line-height: 1.6;
}

.modal button {
    padding: 0.8rem 1.5rem;
    background-color: #1a7de8;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.2s;
}

.modal button:hover {
    background-color: #1465c0;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes scaleUp {
    from {
        transform: scale(0.8);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}