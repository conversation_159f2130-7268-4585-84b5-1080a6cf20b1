

document.addEventListener('dragstart', function(event) {
    // Access the element being dragged
    const draggedElement = event.target;
    
    // Get the ID of the dragged element
    const elementId = draggedElement.id;
    
    // Log the ID or use it as needed
    console.log('Element ID:', elementId);
    
    // Optionally, store the ID in the dataTransfer object for use during drop
    event.dataTransfer.setData('text/plain', elementId);
});

document.addEventListener('dragover', function(event) {
    // Prevent default to allow drop
    event.preventDefault();
});

document.addEventListener('dragenter', function (event) {
    // Check if the drop target has the class 'droppable'
    if (event.target.classList.contains('droppable')) {
        event.target.classList.add('dragenter'); // Add a class for visual feedback
    }
});

// Remove the class when the draggable element leaves the drop target
document.addEventListener('dragleave', function (event) {
    // Check if the drop target has the class 'droppable'
    if (event.target.classList.contains('droppable')) {
        event.target.classList.remove('dragenter'); // Remove the class only if no image is present
    }
});

document.addEventListener('drop', function(event) {
    // Prevent default behavior (e.g., opening as a link)
    event.preventDefault();
    
    // Retrieve the ID from the dataTransfer object
    const elementId = event.dataTransfer.getData('text/plain');
    
    // Log the ID or use it as needed
    console.log('Dropped Element ID:', elementId);

    
    // Check if the drop target has the class 'droppable'
    if (event.target.classList.contains('droppable') || event.target.classList.contains('item')) {
        // Optionally, append the dragged element to the drop zone
        const draggedElement = document.getElementById(elementId);
        event.target.classList.add("dropped");
        event.target.appendChild(draggedElement);
        
        // Set up a MutationObserver to monitor the span for changes
        const observer = new MutationObserver(function (mutationsList) {
            for (const mutation of mutationsList) {
                if (mutation.type === 'childList') {
                    // Check if the img tag is no longer a child of the span
                    if (!event.target.querySelector('img')) {
                        event.target.classList.remove('dropped');
                        observer.disconnect(); // Stop observing once the class is removed
                    }
                }
            }
        });

        // Start observing the span for child node changes
        observer.observe(event.target, { childList: true });
    } else {
        console.log('Drop target is not droppable.');
    }

    // Remove the class only if no image is present
    if (event.target.classList.contains('droppable')) {
        event.target.classList.remove('dragenter');
    }
});

// Function to show the instruction modal
function showInstructionModal() {
    // Create the overlay element
    const overlay = document.createElement('div');
    overlay.classList.add('overlay');
    document.body.appendChild(overlay);

    // Create the modal element
    const modal = document.createElement('div');
    modal.classList.add('modal');
    modal.style.textAlign = 'center';
    modal.style.backgroundColor = "#0b0b23";
    modal.style.color = "rgb(255, 255, 255)";
    modal.style.padding = "20px";
    modal.style.boxShadow = "0 0 4px rgba(203, 193, 193, 0.48)";
    modal.style.maxWidth = "600px";
    modal.style.margin = "auto";

    // Add the title to the modal
    const title = document.createElement('h2');
    title.textContent = 'Mission';
    title.style.color = "gold";
    title.style.marginBottom = "20px";
    modal.appendChild(title);

    // Add the instructions content
    const instructions = document.createElement('div');
    instructions.style.textAlign = 'left';
    instructions.style.marginBottom = '20px';
    instructions.innerHTML = `
        <p style="font-size: 18px; text-align: center; font-family: arial;">Network Setup Challenge</p><br>
        <p style="font-size: 18px; text-align: center; font-family: arial;">Build a basic network by dragging and dropping the images to their proper positions. </p><br>
    `;
    instructions.style.fontFamily = "Arial";
    modal.appendChild(instructions);

    // Add an "Ok" button to the modal
    const okButton = document.createElement('button');
    okButton.fontFamily = "Arial";
    okButton.textContent = 'Start Game';
    okButton.style.marginTop = '10px';
    okButton.style.padding = '10px 20px';
    okButton.style.borderRadius = '5px';
    okButton.style.border = 'none';
    okButton.style.backgroundColor = '#007BFF';
    okButton.style.color = 'white';
    okButton.style.cursor = 'pointer';
    okButton.addEventListener('click', () => {
        // Remove the modal and overlay when the "Ok" button is clicked
        document.body.removeChild(modal);
        document.body.removeChild(overlay);
        
        // Enable interactions on the page
        const allElements = document.querySelectorAll('body > *:not(div)');
        allElements.forEach(element => {
            element.style.pointerEvents = 'auto';
            element.style.filter = 'none';
        });
    });
    modal.appendChild(okButton);

    // Append the modal to the body
    document.body.appendChild(modal);

    // Disable all other elements on the page temporarily
    const allElements = document.querySelectorAll('body > *:not(div)');
    allElements.forEach(element => {
        element.style.pointerEvents = 'none';
        element.style.filter = 'blur(2px)';
    });
}

// Show the instruction modal when the page loads
document.addEventListener('DOMContentLoaded', function() {
    showInstructionModal(); // Then show instructions

    const images = document.querySelectorAll('.img');
    
    // Create a tooltip element
    const tooltip = document.createElement('div');
    tooltip.style.position = 'absolute';
    tooltip.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
    tooltip.style.color = 'white';
    tooltip.style.padding = '5px 10px';
    tooltip.style.borderRadius = '5px';
    tooltip.style.zIndex = '1000';
    tooltip.style.display = 'none';
    tooltip.style.fontFamily = 'Arial, sans-serif';
    tooltip.style.fontSize = '14px';
    document.body.appendChild(tooltip);
    
    // Add event listeners to each image
    images.forEach(img => {
        img.addEventListener('mouseenter', function(e) {
            // Check if the image is inside an element with class 'image-con'
            if (!this.closest('.image-con')) return;
            
            // Get the alt text or use a default if empty
            const altText = this.alt || 'No description available';
            
            // Position the tooltip near the cursor
            tooltip.textContent = altText;
            tooltip.style.display = 'block';
            tooltip.style.left = (e.pageX + 10) + 'px';
            tooltip.style.top = (e.pageY + 10) + 'px';
        });
        
        img.addEventListener('mouseleave', function() {
            tooltip.style.display = 'none';
        });
        
        img.addEventListener('mousemove', function(e) {
            // Only update if inside image-con
            if (!this.closest('.image-con')) return;
            
            // Update tooltip position as mouse moves
            tooltip.style.left = (e.pageX + 10) + 'px';
            tooltip.style.top = (e.pageY + 10) + 'px';
        });
    });

   // Get all elements needed
   const pcCon = document.querySelector('.pc-con');
   const dropBox1 = document.getElementById('1');
   const dropBox2 = document.getElementById('2');
   const dropBox3 = document.getElementById('3');
   const rightCon = document.querySelector('.right-con');
   const img = pcCon.querySelectorAll('img');
   
   // Create a single SVG container for all lines
   const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
   svg.style.position = 'absolute';
   svg.style.top = '0';
   svg.style.left = '0';
   svg.style.width = '100%';
   svg.style.height = '100%';
   svg.style.pointerEvents = 'none';
   svg.style.zIndex = '-1';
   
   rightCon.appendChild(svg);
   
   function drawAllLines() {
       // Clear all previous lines
       while (svg.firstChild) {
           svg.removeChild(svg.firstChild);
       }
       
       const containerRect = rightCon.getBoundingClientRect();
       
       // Draw connections from images to box1
       const box1Rect = dropBox1.getBoundingClientRect();
       const box1X = box1Rect.left + box1Rect.width / 2 - containerRect.left;
       const box1Y = box1Rect.top + box1Rect.height / 2 - containerRect.top;
       
       img.forEach(img => {
           const imgRect = img.getBoundingClientRect();
           const imgX = imgRect.left + imgRect.width / 2 - containerRect.left;
           const imgY = imgRect.top + imgRect.height / 2 - containerRect.top;
           
           const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
           line.setAttribute('x1', imgX.toString());
           line.setAttribute('y1', imgY.toString());
           line.setAttribute('x2', box1X.toString());
           line.setAttribute('y2', box1Y.toString());
           line.setAttribute('stroke', '#ffffffd7');
           line.setAttribute('stroke-width', '2');
           svg.appendChild(line);
       });
       
       // Draw connections between boxes (box1 to box2 to box3)
       const box2Rect = dropBox2.getBoundingClientRect();
       const box2X = box2Rect.left + box2Rect.width / 2 - containerRect.left;
       const box2Y = box2Rect.top + box2Rect.height / 2 - containerRect.top;
       
       const box3Rect = dropBox3.getBoundingClientRect();
       const box3X = box3Rect.left + box3Rect.width / 2 - containerRect.left;
       const box3Y = box3Rect.top + box3Rect.height / 2 - containerRect.top;
       
       // Box1 to Box2
       const line1 = document.createElementNS('http://www.w3.org/2000/svg', 'line');
       line1.setAttribute('x1', box1X.toString());
       line1.setAttribute('y1', box1Y.toString());
       line1.setAttribute('x2', box2X.toString());
       line1.setAttribute('y2', box2Y.toString());
       line1.setAttribute('stroke', '#ffffffd7');
       line1.setAttribute('stroke-width', '2');
       svg.appendChild(line1);
       
       // Box2 to Box3
       const line2 = document.createElementNS('http://www.w3.org/2000/svg', 'line');
       line2.setAttribute('x1', box2X.toString());
       line2.setAttribute('y1', box2Y.toString());
       line2.setAttribute('x2', box3X.toString());
       line2.setAttribute('y2', box3Y.toString());
       line2.setAttribute('stroke', '#ffffffd7');
       line2.setAttribute('stroke-width', '2');
       svg.appendChild(line2);
   }
   
   // Initial draw
   window.addEventListener('load', drawAllLines); // Replaces the initial draw
   
   // Redraw when window is resized
   //window.addEventListener('resize', drawAllLines);
});

// Add an event listener to the submit button
const submitButton = document.querySelector('.btn-submit');
submitButton.addEventListener('click', function() {
    // Check if elements are in correct positions
    const box1 = document.getElementById('1');
    const box2 = document.getElementById('2');
    
    // Check if img0 is in box1, img1 is in box2, etc.
    const isImg0InBox1 = box1.querySelector('#img0') !== null;
    const isImg1InBox2 = box2.querySelector('#img1') !== null;
    
    // Calculate score (2 points for each correct placement)
    let score = 0;
    if (isImg0InBox1) score += 2;
    if (isImg1InBox2) score += 2;
    
    // Calculate percentage (max score is 4 in this case)
    const percentage = (score / 4) * 100;
    
    // Show modal with results
    showResultModal(percentage);
});

function showResultModal(percentage) {
// Create overlay
const overlay = document.createElement('div');
overlay.style.position = 'fixed';
overlay.classList.add('overlay');
overlay.style.top = '0';
overlay.style.left = '0';
overlay.style.width = '100%';
overlay.style.height = '100%';
overlay.style.backgroundColor = 'rgba(0,0,0,0.7)';
overlay.style.zIndex = '1000';
document.body.appendChild(overlay);

// Create modal
const modal = document.createElement('div');
modal.style.position = 'fixed';
modal.classList.add('modal');   
modal.style.top = '50%';
modal.style.left = '50%';
modal.style.transform = 'translate(-50%, -50%)';
modal.style.backgroundColor = '#0b0b23';
modal.style.padding = '30px';
modal.style.borderRadius = '10px';
modal.style.minWidth = '25vw';
modal.style.textAlign = 'center';
modal.style.color = 'white';
modal.style.boxShadow = '0 0 20px rgba(255,255,255,0.2)';
modal.style.zIndex = '1001';

// Show 3-star modal if perfect (≥80%), else show restart modal
if (percentage >= 100) {
    // Perfect score: Show 3 stars
    modal.innerHTML = `
        <h2 style="margin-top: 20px; font-size: 3em;">⭐⭐⭐</h2>
        <h2  style="margin-top: 20px; font-size: 1.5rem;"> Perfect!</h2>
        <p>All items placed correctly!</p>
        <button id="continue-btn" style="margin-top: 20px; padding: 10px 20px; background: #4CAF50; border: none; border-radius: 5px; color: white; cursor: pointer;">
            Continue
        </button>
    `;
} else {
    // Not perfect: Show restart option
    modal.innerHTML = `
        <h2 style="margin-top: 20px; font-size: 3em;">😭😭😭</h2>
        <h2 style="margin-top: 20px; font-size: 1.5rem;">Sorry, Try again</h2>
        <button id="restart-btn" style="margin-top: 20px; padding: 10px 20px; background: #f44336; border: none; border-radius: 5px; color: white; cursor: pointer;">
            Restart
        </button>
    `;
}

document.body.appendChild(modal);

// Handle button clicks
modal.addEventListener('click', (e) => {
    if (e.target.id === 'continue-btn') {
        // Proceed to next level or close modal
        document.body.removeChild(overlay);
        document.body.removeChild(modal);
    } else if (e.target.id === 'restart-btn') {
        // Reload the page to restart
        location.reload();
    }
});
    
    document.body.appendChild(modal);
    
    // Add event listener to continue button
    modal.querySelector('#continue-btn').addEventListener('click', function() {
        document.body.removeChild(modal);
        document.body.removeChild(overlay);
        // You can redirect or do other actions here
        window.location.href = "mainpage.html";
    });
}