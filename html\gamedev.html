<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Network Quiz Game</title>
    <link rel="stylesheet" href="/css/style_gamedev.css">
</head>
<body>
    <div class="container">
        <div class="quiz-header">
            <!-- Hanged Character Display -->
            <div class="hanged-robot">
                <div class="robot-gallows"></div>
                <div class="rope"></div>
                <div class="robot-head inactive-part">
                    <div class="robot-face inactive-part">
                        <div class="robot-eye left-eye"></div>
                        <div class="robot-eye right-eye"></div>
                        <div class="robot-mouth"></div>
                    </div>
                    <div class="robot-antenna left-antenna"></div>
                    <div class="robot-antenna right-antenna"></div>
                    <div class="robot-panel"></div>
                </div>
                <div class="robot-body inactive-part">
                    <div class="robot-panel large-panel"></div>
                    <div class="robot-panel small-panel"></div>
                </div>
                <div class="robot-left-arm inactive-part">
                    <div class="robot-joint"></div>
                </div>
                <div class="robot-right-arm inactive-part">
                    <div class="robot-joint"></div>
                </div>
                <div class="robot-left-leg inactive-part">
                    <div class="robot-joint"></div>
                </div>
                <div class="robot-right-leg inactive-part">
                    <div class="robot-joint"></div>
                </div>
            </div>
            
            <!-- Timer Display -->
            <div class="timer-container">
                <span class="timer-label">Time:</span>
                <span class="timer">30</span>
            </div>
        </div>

        <div class="quiz-title">
            <h1>Network Quiz Challenge</h1>
        </div>

        <div class="quiz-content">
            <div class="question-container">
                <div class="question-body">
                    <p>Question will appear here</p>
                </div>
            </div>
            
            <div class="answers-container">
                <div class="answers-body">
                    <!-- Options will be inserted here by JavaScript -->
                </div>
            </div>
        </div>
        
        <div class="quiz-footer">
            <button id="btn_next">Next</button>
        </div>
    </div>
    
    <script src="/js/gamedev.js"></script>
</body>
</html>