*{
    margin: 0;
    padding: 0;

    font-family: Arial, Helvetica, sans-serif;
}

body{
    background-color: rgb(11, 11, 35);
}

.container{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    padding-bottom: 10px;
}

.box-con{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    box-shadow: 0px 0px 5px rgba(236, 231, 231, 0.397);
    padding: 10px;
    min-width: 80vw;
}

.header{
    width: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.header a{
    font-size: 50px;
    text-decoration: none;
    padding: 0px 10px;
    margin: 0;
    color: white;
}

.score-lbl, .score-txt{
    font-size: 1em;
}

.score-txt{
    margin: 0px 10px;
}

.left-con, .right-con{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    min-height: 100px;
    min-width: 150px;
    border: 2px dashed white;
    margin: 10px;
    color: white;
    position: relative;

}

.left-con{
    background-color: #0b0b23;
    flex-direction: row;
    padding: 20px;
    position: sticky;
    top: 0;
    box-shadow: 0px 0px 10px rgba(236, 231, 231, 0.397);
}

.right-con{
    padding: 10px;
    min-width: 50vw;

}

.item{
    padding: 5px;
    margin: 0px 10px;
    font-size: 14px;
    width: 70px;
    min-height: 90px;
    height: fit-content;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.186);
    transition: 0.3s;
    color: #ffffffd7;
}

.item p{
    margin: 0px 0px 10px 0px;
}

.item img{
    width: 100%;
    cursor: grab;
}

.item:hover{
    transform: scale(1.3);
    background-color: white;
    color: rgb(11, 11, 35);
}

.pc-con{
    width: 100px;

    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-top: 20px;
}

.pc-con img, .drop-box{
    background-color: #0b0b23;
}

.pc-con img{
    border: 1px solid #ffffffd7;
    border-radius: 10%;
    padding: 10px;
    margin-top: 20px;
}

.right-row{
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 100px;
}

.drop-bin{
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 10px;
    min-height: 100px;
}

.dragenter{
    transform: scale(1.1);
    transition: .4s;
    border: dashed 3px rgb(255, 255, 255);
    background-color: rgb(209, 218, 225);
}

.drop-box.dropped{
    background-color: #ffffff;
}

.drop-box{
    min-width: 100px;
    min-height: 100px;
    max-width: 200px;
    max-width: 200px;
    padding: 10px;
    margin: 20px;
    border: 1px solid #ffffffd7;
    border-radius: 10%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.drop-box img{
    width: 100px;
    height: 100%;
}

.btn-submit{
    padding: 7px;
    background-color: rgb(11, 188, 61);
    border-radius: 5%;
    border: none;
    opacity: 0.8;
    transition: .4s;
    color: white;
    font-size: 1.1em;
    cursor: pointer;
}

.btn-submit:hover{
    opacity: 1;
}

/* Overlay styles */
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    animation: fadeIn 0.3s ease-in-out;
}

/* Modal styles */
.modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(255, 225, 0, 0.949);
    z-index: 1000;
    text-align: center;
    animation: scaleUp 0.3s ease-in-out;
}

/* Fade-in animation for overlay */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Scale-up animation for modal */
@keyframes scaleUp {
    from {
        transform: translate(-50%, -50%) scale(0.8);
        opacity: 0;
    }
    to {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
}