:root {
    --primary-color: rgb(15, 0, 227);
    --secondary-color: #a29bfe;
    --dark-color: #2d3436;
    --light-color: #f5f6fa;
    --success-color: #00b894;
    --text-color: #2d3436;
    --text-light: #636e72;
    --border-radius: 8px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

@keyframes visibility{
    0%{
        opacity: 0;

    }
    100%{
        opacity: 1;

    }
    
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: rgb(11, 8, 16);
    animation: visibility .5s 1;
}

.game-tracker {
    width: 100%;
    max-width: 90vw;
    min-height: 80vh;
    height: 100%;
    background: white;
    border-radius: 10px;

    overflow: hidden;
    background-color: rgb(11, 8, 16);
}

.tracker-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background-color: var(--primary-color);
    color: rgb(15, 0, 227);
    background-color: white;
}

.tracker-header h1 {
    font-size: 1.5rem;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    color: rgb(15, 0, 227);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 5px 10px;
    border-radius: 50%;
    transition: var(--transition);
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.tracker-content {
    padding: 20px;
    border: 2px solid rgb(15, 0, 227);  
}

/* Achievements */
.achievements {
    grid-column: 1 / -1;
    margin: 3rem;
    border-top: 1px solid var(--gray);
}

.achievements h2 {
    margin-bottom: 1.5rem;
    text-align: center;
    font-size: 1.8rem;
    color: rgb(15, 0, 227);
    letter-spacing: 1px;
}

.achievements-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 1.8rem;
}

.achievement {
    padding: 1.8rem;
    border-radius: 12px;
    text-align: center;
    color: white;
    background-color: rgb(8, 39, 197);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.05);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.achievement.unlocked {
    border: 1px solid var(--success);
}

.achievement.locked {
    filter: grayscale(70%);
    opacity: 0.6;
}

.achievement.unlocked::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 40px 40px 0;
    border-color: transparent var(--success) transparent transparent;
}

.achievement.unlocked::before {
    content: '✓';
    position: absolute;
    top: 4px;
    right: 4px;
    color: white;
    font-size: 1rem;
    z-index: 1;
}

.achievement-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.2));
}

.achievement h3 {
    color: var(--primary);
    margin-bottom: 0.8rem;
    font-size: 1.2rem;
}

.achievement p {
    font-size: 0.9rem;
    color: var(--dark-gray);
    line-height: 1.5;
}

.achievement.unlocked:hover {
    transform: translateY(-8px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

/* Glow effects for interactive elements */
.btn-edit, .btn-save, .achievement.unlocked, .badge:not(.locked) {
    position: relative;
    overflow: hidden;
}

.btn-edit::after, .btn-save::after, 
.achievement.unlocked::after, .badge:not(.locked)::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
        to bottom right,
        rgba(255, 255, 255, 0) 45%,
        rgba(255, 255, 255, 0.1) 50%,
        rgba(255, 255, 255, 0) 55%
    );
    transform: rotate(30deg);
    animation: shine 3s infinite;
    opacity: 0;
}

@keyframes shine {
    0% {
        transform: rotate(30deg) translate(-30%, -30%);
        opacity: 0;
    }
    20% {
        opacity: 0.5;
    }
    100% {
        transform: rotate(30deg) translate(30%, 30%);
        opacity: 0;
    }
}

.progress-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.progress-card {
    background: white;
    padding: 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.progress-container {
    margin-bottom: 20px;
}

.progress-bar {
    height: 10px;
    background: #dfe6e9;
    border-radius: 5px;
    margin-bottom: 5px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--primary-color);
    border-radius: 5px;
}

.progress-text {
    font-size: 0.8rem;
    color: var(--text-light);
}

.stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.stat {
    text-align: center;
}

.stat h3 {
    font-size: 0.9rem;
    color: var(--text-light);
    margin-bottom: 5px;
}

.stat p {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--primary-color);
}

@media (max-width: 768px) {
    .progress-stats {
        grid-template-columns: 1fr;
    }
    
    .achievements-grid {
        grid-template-columns: 1fr;
    }
    
    body {
        padding: 10px;
    }
}