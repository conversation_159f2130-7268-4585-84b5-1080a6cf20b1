<?php
include 'dbconnection.php';

$message = false;

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $password = $_POST['password'];
    $username = $_POST['username'];

    // Prepare and execute
    $stmt = $conn->prepare("SELECT username, password FROM user_account WHERE username = ? AND password = ?");
    $stmt->bind_param("ss", $username, $password);
    $stmt->execute();
    $stmt->store_result();

    if ($stmt->num_rows > 0) {
        $stmt->bind_result($db_username, $db_password);
        $stmt->fetch();
        var_dump("$db_username $db_password<br>");
        var_dump("$username $password <br>");

        if ($username === $db_username) {
            header("Location: mainpage copy.html");  
        } else {
            $message = "Invalid username or password";
            echo $message;
        }
    } else {
        //echo "<script>triggerButton()</script>";
        //header("Location: login.html");  
        $message = true;
        echo '
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Document</title>

            <!-- Bootstrap 4 CSS -->
            <link rel="stylesheet" href="bootstrap.min.css">
            <script src="jquery.min.js"></script>
            <script src="bootstrap.bundle.min.js"></script>

            <link rel="stylesheet" href="style_login_signup.css">
        </head>
        <body>
        <button type="button" class="btn btn-primary d-none" id="warning_modal" data-toggle="modal" data-target="#exampleModalCenter">
                Launch demo modal
            </button>
      

      <div class="modal fade" id="exampleModalCenter" data-keyboard="false" data-backdrop="static" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
          <div class="modal-content bg-dark text-white">
            <div class="modal-header">
              <h5 class="modal-title" id="exampleModalLongTitle">Warning </h5>

            </div>
            <div class="modal-body">
              Invalid Payer Name or Password!!
            </div>
            <div class="modal-footer">
              <button type="button" onclick="returnButton()" class="btn btn-primary">Return</button>
            </div>
          </div>
        </div>
        
            <!-- jQuery (Offline) & Bootstrap JS -->
            <script src="jquery.min.js"></script>
            <script src="bootstrap.bundle.min.js"></script>

            <!-- jQuery Script for Sign Up Button -->
            <script>
            $(document).ready(function(){
                $("#signupButton").click(function(){
                    alert("Redirecting to Sign Up page...");
                    // window.location.href = "signup.html"; // Uncomment to redirect
                });
            });
        </script>
        </body>
        </html>';
    }

    $stmt->close();
    $conn->close();
}

?>

<script>
    let login_fail = `<?php echo $message; ?>`;
    console.log(login_fail);
    if (login_fail === "1") {
        triggerButton();
        //alert("Invalid username or password");
    }


    function triggerButton() {
        document.getElementById("warning_modal").click();
    };

    function returnButton() {
        window.location.href = 'login.html'
    }

</script>