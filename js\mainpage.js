function toggle_open_profile() {
    document.getElementById("toggle_profile").style.width = "250px";
}

function toggle_close_profile() {
    document.getElementById("toggle_profile").style.width = "0%";
}

// Function to lock levels
// function lockLevels() {
//     const levelsContainer = document.querySelector('.cont-levels-row');
//     const levelButtons = levelsContainer.querySelectorAll('.cont-levels-column button');
    
//     // Example: Lock all levels above level 3 (you can change this condition)
//     const unlockedLevels = 0; // This would normally come from user data
    
//     levelButtons.forEach((button, index) => {
//         const levelNumber = index + 1;
        
//         if (levelNumber > unlockedLevels) {
//             // Add locked class for styling
//             button.classList.add('locked');
            
//             // Replace content with lock icon
//             button.innerHTML = `
//                 <div class="locked-level">
//                     <img src="/images/lock.png" alt="Locked">
//                 </div>
//             `;
            
//             // Remove click event
//             button.onclick = function(e) {
//                 e.preventDefault();
//                 alert('This level is locked. Complete previous levels to unlock.');
//             };
//         }
//     });
// }

// Call the function when the page loads
window.onload = function() {
    lockLevels();
    
    // Your existing toggle profile functions
    // ...
};