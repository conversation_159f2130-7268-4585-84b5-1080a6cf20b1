document.addEventListener('DOMContentLoaded', function() {
   // Game variables
   let currentQuestionIndex = 0;
   let score = 0;
   let timer;
   let timeLeft = 30;
   let hangmanStage = 0;
   const maxHangmanStages = 6;
   
   // DOM elements
   const questionElement = document.querySelector('.question-body p');
   const optionsContainer = document.querySelector('.answers-body');
   const nextButton = document.getElementById('btn_next');
   const timerElement = document.querySelector('.timer');
   
   // Questions data
   const questions = [
       {
           question: "Which device connects multiple computers in a LAN and forwards data based on MAC addresses?",
           options: ["Router", "Switch", "Hub", "Modem"],
           correctAnswer: 1
       },
       {
        question: "Which device connects multiple computers in a LAN and forwards data based on MAC addresses?",
        options: ["Switch", "Router", "Hub", "Modem"],
        correctAnswer: 0
        },
        {
            question: "Which device connects multiple computers in a LAN and forwards data based on MAC addresses?",
            options: ["Hub", "Modem", "Router", "Switch"],
            correctAnswer: 3
        }
       // ... (include all your other questions here)
   ];

    // Initialize the game
    showInstructionModal();
    
    function showInstructionModal() {
        // Create the overlay element
        const overlay = document.createElement('div');
        overlay.classList.add('overlay');
        document.body.appendChild(overlay);
    
        // Create the modal element
        const modal = document.createElement('div');
        modal.classList.add('modal');
        modal.style.textAlign = 'center';
        modal.style.backgroundColor = "#0b0b23";
        modal.style.color = "rgb(255, 255, 255)";
        modal.style.padding = "30px";
        modal.style.borderRadius = "10px";
        modal.style.boxShadow = "0 0 20px rgba(203, 193, 193, 0.48)";
        modal.style.maxWidth = "600px";
        modal.style.width = "90%";
        modal.style.margin = "auto";
        modal.style.position = "relative";
        modal.style.animation = "fadeIn 0.3s ease-in-out";
    
        // Add the title to the modal
        const title = document.createElement('h2');
        title.textContent = 'Mission';
        title.style.color = "gold";
        title.style.fontSize = "1.8em";
        title.style.marginBottom = "20px";
        modal.appendChild(title);
    
        // Add the instructions content
        const instructions = document.createElement('div');
        instructions.style.textAlign = 'left';
        instructions.style.marginBottom = '30px';
        instructions.innerHTML = `
            <h3 style="color: #1a7de8; margin-bottom: 15px;">Quiz Challenge</h3>
            <div style="margin-bottom: 20px;">
                <h4 style="color: #ffcc00; margin-bottom: 10px;">📚 How to Play:</h4>
                <p style="margin-bottom: 10px;"><span style="font-size: 1.2em;">❓</span> Answer multiple-choice questions about network cables</p>
                <p style="margin-bottom: 10px;"><span style="font-size: 1.2em;">✅</span> <strong>Correct answer?</strong> Move to the next question!</p>
                <p style="margin-bottom: 10px;"><span style="font-size: 1.2em;">❌</span> <strong>Wrong answer?</strong> Lose a life point</p>
                <p style="margin-bottom: 10px;"><span style="font-size: 1.2em;">🏆</span> <strong>Goal:</strong> Survive all questions!</p>
            </div>
            <p style="font-style: italic;"><span style="font-size: 1.2em;">💡</span> <strong>Pro Tip:</strong> Read carefully!</p>
        `;
        instructions.style.fontFamily = "Arial";
        instructions.style.fontSize = "1.1em";
        instructions.style.lineHeight = "1.6";
        modal.appendChild(instructions);
    
        // Add an "Ok" button to the modal
        const okButton = document.createElement('button');
        okButton.textContent = 'Start Game';
        okButton.style.marginTop = '20px';
        okButton.style.padding = '12px 30px';
        okButton.style.borderRadius = '5px';
        okButton.style.border = 'none';
        okButton.style.fontSize = '1.1em';
        okButton.style.backgroundColor = '#1a7de8';
        okButton.style.color = 'white';
        okButton.style.cursor = 'pointer';
        okButton.style.transition = 'background-color 0.2s';
        
        okButton.addEventListener('mouseenter', () => {
            okButton.style.backgroundColor = '#1465c0';
        });
        
        okButton.addEventListener('mouseleave', () => {
            okButton.style.backgroundColor = '#1a7de8';
        });
        
        okButton.addEventListener('click', () => {
            document.body.removeChild(overlay);
            //document.body.removeChild(modal);
            
            // Enable interactions on the page
            const allElements = document.querySelectorAll('body > *:not(div)');
            allElements.forEach(element => {
                element.style.pointerEvents = 'auto';
                element.style.filter = 'none';
            });
            
            // Start the game
            displayQuestion();
        });
        
        modal.appendChild(okButton);
    
        // Append the modal to the overlay
        overlay.appendChild(modal);
    
        // Disable all other elements on the page temporarily
        const allElements = document.querySelectorAll('body > *:not(div)');
        allElements.forEach(element => {
            element.style.pointerEvents = 'none';
            element.style.filter = 'blur(2px)';
        });
    
        // Focus the button for accessibility
        okButton.focus();
    }

    function displayQuestion() {
        if (currentQuestionIndex < questions.length) {
            startTimer();
            resetHangman();
            
            const currentQuestion = questions[currentQuestionIndex];
            questionElement.textContent = currentQuestion.question;
            optionsContainer.innerHTML = '';
            
            currentQuestion.options.forEach((option, index) => {
                const optionDiv = document.createElement('div');
                optionDiv.className = 'option';
                
                const radioInput = document.createElement('input');
                radioInput.type = 'radio';
                radioInput.name = 'opt';
                radioInput.id = 'opt' + index;
                radioInput.value = index;
                
                const label = document.createElement('label');
                label.htmlFor = 'opt' + index;
                label.textContent = option;
                
                optionDiv.appendChild(radioInput);
                optionDiv.appendChild(label);
                optionsContainer.appendChild(optionDiv);
            });
        } else {
            showFinalScore();
        }
    }

    function checkAnswerAndMoveNext() {
        const selectedOption = document.querySelector('input[name="opt"]:checked');
        
        if (!selectedOption) {
            alert('Please select an answer!');
            return false;
        }
        
        clearInterval(timer);
        
        const selectedAnswer = parseInt(selectedOption.value);
        const currentQuestion = questions[currentQuestionIndex];
        const isCorrect = selectedAnswer === currentQuestion.correctAnswer;
        
        if (isCorrect) {
            score++;
        } else {
            increaseHangmanStage();
        }
        
        if (hangmanStage >= maxHangmanStages) {
            showGameOver();
            return;
        }
        
        setTimeout(() => {
            currentQuestionIndex++;
            if (currentQuestionIndex < questions.length) {
                displayQuestion();
            } else {
                showFinalScore();
            }
        }, 1000);
        
        return isCorrect;
    }

    // Function to show final score in a modal
    function showFinalScore() {
        const percentage = Math.round((score / questions.length) * 100);
        let message= `<h2>Congrats</h2> <br>`;
        
        if (percentage >= 80) {
            message += "Excellent! <br> ⭐⭐⭐";
        } else if (percentage >= 60) {
            message += "Good job! <br> ⭐⭐";
            stars = '⭐⭐';
        } else if (percentage >= 40) {
            message += "Not bad! <br> ⭐";
        } else {
            message += "Keep learning! <br> ⭐";
        }
        
        // Create modal overlay
        const modalOverlay = document.createElement('div');
        modalOverlay.style.position = 'fixed';
        modalOverlay.style.top = '0';
        modalOverlay.style.left = '0';
        modalOverlay.style.width = '100%';
        modalOverlay.style.height = '100%';
        modalOverlay.style.backgroundColor = 'rgba(0,0,0,0.5)';
        modalOverlay.style.display = 'flex';
        modalOverlay.style.justifyContent = 'center';
        modalOverlay.style.alignItems = 'center';
        modalOverlay.style.zIndex = '1000';
        modalOverlay.id = 'modal-overlay';
        
        // Create modal content
        const modalContent = document.createElement('div');
        modalContent.style.backgroundColor = 'rgb(11, 11, 35)';
        modalContent.style.padding = '2rem';
        modalContent.style.borderRadius = '8px';
        modalContent.style.boxShadow = '0 4px 8px rgba(0,0,0,0.2)';
        modalContent.style.maxWidth = '500px';
        modalContent.style.width = '80%';
        modalContent.style.textAlign = 'center';
        
        // Add message to modal
        const scoreMessage = document.createElement('p');
        scoreMessage.innerHTML = message;
        scoreMessage.style.fontSize = "1.5em";
        scoreMessage.style.fontFamily = 'Arial, sans-serif';
        scoreMessage.style.marginBottom = '1.5rem';
        scoreMessage.style.color = 'white';
        
        // Add close button
        const closeButton = document.createElement('button');
        closeButton.textContent = 'OK';
        closeButton.style.padding = '0.5rem 1.5rem';
        closeButton.style.backgroundColor = '#4CAF50';
        closeButton.style.color = 'white';
        closeButton.style.border = 'none';
        closeButton.style.borderRadius = '4px';
        closeButton.style.cursor = 'pointer';
        closeButton.style.fontSize = '1rem';
        closeButton.addEventListener('click', function() {
            window.location.href = "mainpage.html";
        }); 
        
        // Add event listener to close modal
        closeButton.addEventListener('click', function() {
            document.body.removeChild(modalOverlay);
            // Re-enable the next button if you want to allow restarting
            nextButton.disabled = false;
        });
        
        // Assemble modal
        modalContent.appendChild(scoreMessage);
        modalContent.appendChild(closeButton);
        modalOverlay.appendChild(modalContent);
        
        // Disable all other elements on the page
        const allElements = document.querySelectorAll('body > *:not(div)');
        allElements.forEach(element => {
            element.style.pointerEvents = 'none'; // Disable interactions
            element.style.filter = 'blur(2px)'; // Optional: Add a blur effect
        });

        // Add modal to the page
        document.body.appendChild(modalOverlay);
        
        // Add event listener to handle ESC key
        modalOverlay.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                document.body.removeChild(modalOverlay);
                // Enable interactions on the page
                const allElements = document.querySelectorAll('body > *:not(div)');
                allElements.forEach(element => {
                    element.style.pointerEvents = 'auto';
                    element.style.filter = 'none';
                });
            }
        });
        
        // Focus the close button for accessibility
        closeButton.focus();
    }
    function showGameOver() {
        // Create the overlay element
        const overlay = document.createElement('div');
        overlay.classList.add('overlay');
        document.body.appendChild(overlay);


        // Create modal overlay
        const modalOverlay = document.createElement('div');
        modalOverlay.style.position = 'fixed';
        modalOverlay.style.top = '0';
        modalOverlay.style.left = '0';
        modalOverlay.style.width = '100%';
        modalOverlay.style.height = '100%';
        modalOverlay.style.backgroundColor = 'rgba(11, 11, 35, 0.9)';
        modalOverlay.style.display = 'flex';
        modalOverlay.style.justifyContent = 'center';
        modalOverlay.style.alignItems = 'center';
        modalOverlay.style.zIndex = '1000';
        modalOverlay.id = 'modal-overlay';
        modalOverlay.classList.add('overlay');
    
        // Create modal content
        const modalContent = document.createElement('div');
        modalContent.style.backgroundColor = 'rgb(11, 11, 35)';
        modalContent.style.padding = '2rem';
        modalContent.style.borderRadius = '8px';
        modalContent.style.boxShadow = '0 0 4px rgba(203, 193, 193, 0.48)   ';
        modalContent.style.maxWidth = '500px';
        modalContent.style.width = '80%';
        modalContent.style.textAlign = 'center';
    
        // Add title
        const title = document.createElement('h2');
        title.textContent = 'Game Over!';
        title.style.color = 'red';
        title.style.marginBottom = '1rem';
        title.style.fontFamily = 'Arial, sans-serif';
    
        // Add score message
        const scoreMessage = document.createElement('p');
        scoreMessage.textContent = `Your final score: ${score}/${questions.length}`;
        scoreMessage.style.fontFamily = 'Arial, sans-serif';
        scoreMessage.style.marginBottom = '1.5rem';
        scoreMessage.style.color = 'White';
    
        // Add restart button
        const restartButton = document.createElement('button');
        restartButton.textContent = 'Restart Quiz';
        restartButton.id = 'restart-btn';
        restartButton.style.padding = '0.5rem 1.5rem';
        restartButton.style.backgroundColor = '#1a7de8';
        restartButton.style.color = 'white';
        restartButton.style.border = 'none';
        restartButton.style.borderRadius = '4px';
        restartButton.style.cursor = 'pointer';
        restartButton.style.fontSize = '1rem';
        restartButton.style.marginTop = '1rem';
    
        // Assemble modal
        modalContent.appendChild(title);
        modalContent.appendChild(scoreMessage);
        modalContent.appendChild(restartButton);
        modalOverlay.appendChild(modalContent);
    
        // Add to document
        document.body.appendChild(modalOverlay);
    
        // Add event listener
        restartButton.addEventListener('click', restartQuiz);
    }


    function restartQuiz() {
        currentQuestionIndex = 0;
        score = 0;
        clearInterval(timer);
        resetHangman();
        
        const modal = document.querySelector('#modal-overlay');
        if (modal) modal.remove();

        const modaloverlay = document.querySelector('.overlay');
        if (modaloverlay) modaloverlay.remove();
        
        const gameOverModal = document.querySelector('[style*="position:fixed; top:0; left:0"]');
        if (gameOverModal) gameOverModal.remove();
        
        nextButton.disabled = false;
        displayQuestion();
    }

    // Timer functions
    function startTimer() {
        if (timer) clearInterval(timer);
        timeLeft = 30;
        updateTimerDisplay();
        
        timer = setInterval(() => {
            timeLeft--;
            updateTimerDisplay();
            
            if (timeLeft <= 0) {
                clearInterval(timer);
                timeUp();
            }
        }, 1000);
    }

    function updateTimerDisplay() {
        if (timerElement) {
            timerElement.textContent = timeLeft;
            
            if (timeLeft <= 10) {
                timerElement.style.color = 'red';
            } else if (timeLeft <= 20) {
                timerElement.style.color = 'orange';
            } else {
                timerElement.style.color = '#ffcc00';
            }
        }
    }

    function timeUp() {
        if (timerElement) {
            timerElement.textContent = "TIME UP!";
        }
        
        increaseHangmanStage();
        
        if (hangmanStage >= maxHangmanStages) {
            showGameOver();
        } else {
            setTimeout(() => {
                currentQuestionIndex++;
                if (currentQuestionIndex < questions.length) {
                    displayQuestion();
                } else {
                    showFinalScore();
                }
            }, 1500);
        }
    }

    // Hangman functions
    function increaseHangmanStage() {
        hangmanStage++;
        updateHangmanDisplay();
    }

    function updateHangmanDisplay() {
        const parts = [
            document.querySelector('.robot-head'),
            document.querySelector('.robot-body'),
            document.querySelector('.robot-left-arm'),
            document.querySelector('.robot-right-arm'),
            document.querySelector('.robot-left-leg'),
            document.querySelector('.robot-right-leg'),
            document.querySelector('.robot-face')
        ];
        
        // Reset all parts to inactive first
        parts.forEach(part => {
            if (part) {
                part.classList.remove('active-part');
                part.classList.add('inactive-part');
            }
        });
        
        // Activate parts up to current stage
        for (let i = 0; i < hangmanStage; i++) {
            if (parts[i]) {
                parts[i].classList.remove('inactive-part');
                parts[i].classList.add('active-part');
            }
        }
        
        // Special case: always show face when head is active
        if (hangmanStage >= 1) {
            document.querySelector('.robot-face').classList.remove('inactive-part');
            document.querySelector('.robot-face').classList.add('active-part');
        }
    }
    
    // Initialize with all parts visible but inactive
    function resetHangman() {
        hangmanStage = 0;
        updateHangmanDisplay();
    }

    // Event listeners
    if (nextButton) {
        nextButton.addEventListener('click', checkAnswerAndMoveNext);
    }
});