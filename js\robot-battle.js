// Game state
const gameState = {
    playerMaxHealth: 100,
    playerHealth: 100,
    enemyMaxHealth: 150,
    enemyHealth: 150,
    currentQuestion: 0,
    timer: null,
    timeLeft: 10,
    isGameActive: false
};

// Questions array
const questions = [
    {
        question: "What is the main function of a CPU?",
        options: ["Store data", "Process instructions", "Display graphics", "Connect to the internet"],
        correct: 1
    },
    {
        question: "Which of these is NOT a programming language?",
        options: ["Java", "Python", "HTML", "C++"],
        correct: 2
    },
    {
        question: "What does RAM stand for?",
        options: ["Random Access Memory", "Read-Only Memory", "Remote Access Module", "Rapid Application Management"],
        correct: 0
    },
    {
        question: "Which company created the first smartphone?",
        options: ["Apple", "Samsung", "IBM", "Nokia"],
        correct: 2
    },
    {
        question: "What is the binary code for the number 10?",
        options: ["1010", "1000", "1100", "1001"],
        correct: 0
    },
    {
        question: "Which of these is a type of computer network?",
        options: ["LAN", "TAN", "CAN", "BAN"],
        correct: 0
    },
    {
        question: "What does HTTP stand for?",
        options: ["Hyper Text Transfer Protocol", "High Tech Transfer Process", "Hyper Transfer Text Protocol", "Home Tool Transfer Protocol"],
        correct: 0
    },
    {
        question: "Which of these is NOT a web browser?",
        options: ["Chrome", "Firefox", "Excel", "Safari"],
        correct: 2
    },
    {
        question: "What is the function of an operating system?",
        options: ["Run applications", "Manage hardware resources", "Create documents", "Connect to the internet"],
        correct: 1
    },
    {
        question: "Which of these is a cloud storage service?",
        options: ["Dropbox", "Photoshop", "Excel", "Notepad"],
        correct: 0
    }
];

// DOM Elements
const questionText = document.querySelector('.question-text');
const optionButtons = document.querySelectorAll('.option-btn');
const timerBar = document.querySelector('.timer-bar');
const timerText = document.querySelector('.timer-text');
const playerHealthBar = document.querySelector('.player-health');
const enemyHealthBar = document.querySelector('.enemy-health');
const playerHealthValue = document.querySelector('.player-side .health-value');
const enemyHealthValue = document.querySelector('.enemy-side .health-value');
const playerRobot = document.querySelector('.player-robot');
const enemyRobot = document.querySelector('.enemy-robot');
const instructionsModal = document.querySelector('.instructions-modal');
const resultsModal = document.querySelector('.results-modal');
const resultTitle = document.querySelector('.result-title');
const resultMessage = document.querySelector('.result-message');
const startButton = document.querySelector('.start-btn');
const replayButton = document.querySelector('.replay-btn');

// Initialize game
function initGame() {
    gameState.playerHealth = gameState.playerMaxHealth;
    gameState.enemyHealth = gameState.enemyMaxHealth;
    gameState.currentQuestion = 0;
    gameState.isGameActive = true;
    
    updateHealthBars();
    loadQuestion();
    startTimer();
    
    // Show instructions modal on first load
    instructionsModal.style.display = 'flex';
}

// Load question
function loadQuestion() {
    // Shuffle questions if we've gone through all of them
    if (gameState.currentQuestion >= questions.length) {
        shuffleArray(questions);
        gameState.currentQuestion = 0;
    }
    
    const currentQ = questions[gameState.currentQuestion];
    questionText.textContent = currentQ.question;
    
    optionButtons.forEach((button, index) => {
        button.textContent = currentQ.options[index];
        button.disabled = false;
        button.classList.remove('correct', 'incorrect');
    });
}

// Start timer
function startTimer() {
    gameState.timeLeft = 10;
    timerText.textContent = gameState.timeLeft;
    timerBar.style.width = '100%';
    
    if (gameState.timer) {
        clearInterval(gameState.timer);
    }
    
    gameState.timer = setInterval(() => {
        gameState.timeLeft--;
        timerText.textContent = gameState.timeLeft;
        timerBar.style.width = `${(gameState.timeLeft / 10) * 100}%`;
        
        if (gameState.timeLeft <= 0) {
            clearInterval(gameState.timer);
            handleTimeout();
        }
    }, 1000);
}

// Update health bars
function updateHealthBars() {
    // Update visual health bars
    playerHealthBar.style.width = `${(gameState.playerHealth / gameState.playerMaxHealth) * 100}%`;
    enemyHealthBar.style.width = `${(gameState.enemyHealth / gameState.enemyMaxHealth) * 100}%`;
    
    // Update health text values
    playerHealthValue.textContent = `${gameState.playerHealth}/${gameState.playerMaxHealth}`;
    enemyHealthValue.textContent = `${gameState.enemyHealth}/${gameState.enemyMaxHealth}`;
    
    // Check for game end conditions
    if (gameState.playerHealth <= 0) {
        endGame(false);
    } else if (gameState.enemyHealth <= 0) {
        endGame(true);
    }
}

// Handle player attack
function playerAttack() {
    // Play attack animation
    playerRobot.classList.add('attack-animation');
    
    // Calculate damage (could be adjusted for difficulty)
    const damage = 20;
    
    // Apply damage after animation delay
    setTimeout(() => {
        // Apply damage to enemy
        gameState.enemyHealth -= damage;
        
        // Show damage effect on enemy
        enemyRobot.querySelector('.damage-effect').style.opacity = 1;
        
        // Update health bars
        updateHealthBars();
        
        // Reset animations
        setTimeout(() => {
            playerRobot.classList.remove('attack-animation');
            enemyRobot.querySelector('.damage-effect').style.opacity = 0;
            
            // Move to next question if game still active
            if (gameState.isGameActive) {
                gameState.currentQuestion++;
                loadQuestion();
                startTimer();
            }
        }, 500);
    }, 300);
}

// Handle enemy attack
function enemyAttack() {
    // Play attack animation
    enemyRobot.classList.add('enemy-attack-animation');
    
    // Calculate damage (could be adjusted for difficulty)
    const damage = 20;
    
    // Apply damage after animation delay
    setTimeout(() => {
        // Apply damage to player
        gameState.playerHealth -= damage;
        
        // Show damage effect on player
        playerRobot.querySelector('.damage-effect').style.opacity = 1;
        
        // Update health bars
        updateHealthBars();
        
        // Reset animations
        setTimeout(() => {
            enemyRobot.classList.remove('enemy-attack-animation');
            playerRobot.querySelector('.damage-effect').style.opacity = 0;
            
            // Move to next question if game still active
            if (gameState.isGameActive) {
                gameState.currentQuestion++;
                loadQuestion();
                startTimer();
            }
        }, 500);
    }, 300);
}

// Handle answer selection
function handleAnswer(index) {
    // Prevent multiple answers
    if (!gameState.isGameActive) return;
    
    // Disable all buttons
    optionButtons.forEach(btn => btn.disabled = true);
    
    // Stop the timer
    clearInterval(gameState.timer);
    
    // Get current question
    const currentQ = questions[gameState.currentQuestion];
    
    // Check if answer is correct
    if (index === currentQ.correct) {
        // Highlight correct answer
        optionButtons[index].classList.add('correct');
        
        // Player attacks enemy
        playerAttack();
    } else {
        // Highlight correct and incorrect answers
        optionButtons[index].classList.add('incorrect');
        optionButtons[currentQ.correct].classList.add('correct');
        
        // Enemy attacks player
        enemyAttack();
    }
}

// Handle timeout (no answer selected)
function handleTimeout() {
    // Prevent multiple timeouts
    if (!gameState.isGameActive) return;
    
    // Disable all buttons
    optionButtons.forEach(btn => btn.disabled = true);
    
    // Highlight correct answer
    const currentQ = questions[gameState.currentQuestion];
    optionButtons[currentQ.correct].classList.add('correct');
    
    // Enemy attacks player
    enemyAttack();
}

// End game
function endGame(playerWon) {
    // Set game as inactive
    gameState.isGameActive = false;
    
    // Clear any running timers
    clearInterval(gameState.timer);
    
    // Update result modal content
    if (playerWon) {
        resultTitle.textContent = "VICTORY!";
        resultMessage.textContent = "You defeated the enemy robot!";
    } else {
        resultTitle.textContent = "DEFEAT!";
        resultMessage.textContent = "Your robot was destroyed!";
    }
    
    // Show results modal
    setTimeout(() => {
        resultsModal.style.display = 'flex';
    }, 1000);
}

// Shuffle array (Fisher-Yates algorithm)
function shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
}

// Event Listeners
optionButtons.forEach((button, index) => {
    button.addEventListener('click', () => handleAnswer(index));
});

startButton.addEventListener('click', () => {
    instructionsModal.style.display = 'none';
    gameState.isGameActive = true;
});

replayButton.addEventListener('click', () => {
    resultsModal.style.display = 'none';
    initGame();
});

// Initialize the game when the page loads
document.addEventListener('DOMContentLoaded', initGame);
