document.addEventListener('DOMContentLoaded', function() {
    showInstructionModal();

    // Sample questions data with correct answers
    const questions = [
        {
            question: "Which device connects multiple computers in a LAN and forwards data based on MAC addresses?",
            options: [
                "Router",
                "Switch",
                "Hub",
                "Modem"
            ],
            correctAnswer: 1
        },
        {
            question: "What device converts digital signals to analog for telephone line communication?",
            options: [
                "Modem",
                "Router",
                "Access Point",
                "Firewall  "
            ],
            correctAnswer: 0
        },
        {
            question: "Which device broadcasts data to all connected devices in a network?",
            options: [
                "Switch",
                "Router",
                "Hub",
                "Repeater"
            ],
            correctAnswer: 2
        },
        {
            question: "A network confined to a single building is called a ___?",
            options: [
                "LAN",
                "WAN",
                "MAN",
                "PAN"
            ],
            correctAnswer: 0
        },
        {
            question: "Which network type covers a large geographic area like a country?",
            options: [
                "LAN",
                "PAN",
                "WAN",
                "MAN"
            ],
            correctAnswer: 2
        },
        {
            question: "Bluetooth devices typically form what kind of network?",
            options: [
                "LAN",
                "MAN",
                "PAN",
                "VPN"
            ],
            correctAnswer: 2
        },
        {
            question: "In which topology do all devices connect to a central cable (backbone)?",
            options: [
                "Star",
                "BUS",
                "Ring",
                "Mesh"
            ],
            correctAnswer: 1
        },
        {
            question: "Which topology ensures all devices are connected to every other device?",
            options: [
                "Star",
                "Hybrid",
                "Ring",
                "Mesh"
            ],
            correctAnswer: 3
        },
        {
            question: "If one device fails in a star topology, what happens to the rest?",
            options: [
                "The whole network fails",
                "Half the network fails",
                "Data loops endlessly",
                "Only that device is affected"
            ],
            correctAnswer: 3
        },
        {
            question: "Which device is used to connect two different networks (e.g., LAN to WAN)?",
            options: [
                "Router",
                "Switch",
                "Hub",
                "Repeater"
            ],
            correctAnswer: 0
        },
        {
            question: "A city-wide network is called a ___?",
            options: [
                "MAN",
                "LAN",
                "WAN",
                "SAN"
            ],
            correctAnswer: 0
        },
        {
            question: "Which topology is easiest to troubleshoot due to centralized control?",
            options: [
                "Bus",
                "Ring",
                "Star",
                "Mesh"
            ],
            correctAnswer: 2
        },
        {
            question: "What device extends Wi-Fi coverage in a large area?",
            options: [
                "Modem",
                "Access Point",
                "Firewall",
                "Switch"
            ],
            correctAnswer: 1
        },
        {
            question: "Which network type is used for high-speed storage (e.g., servers)?",
            options: [
                "VPN",
                "SAN",
                "LAN",
                "WAN"
            ],
            correctAnswer: 1
        },
        {
            question: "In a ring topology, how is data transmitted?",
            options: [
                "Sequentially in one direction",
                "Randomly",
                "Via broadcast",
                "Only wirelessly  "
            ],
            correctAnswer: 0
        }

    ];

    let currentQuestionIndex = 0;
    let score = 0;
    const questionElement = document.querySelector('.left-con-body p');
    const optionsContainer = document.querySelector('.right-con-body');
    const nextButton = document.getElementById('btn_next');
    //const scoreDisplay = document.createElement('div');
    //scoreDisplay.className = 'score-display';
    //document.body.appendChild(scoreDisplay);

    // Function to display the current question and options
    function displayQuestion() {
        if (currentQuestionIndex < questions.length) {
            const currentQuestion = questions[currentQuestionIndex];
            
            // Display the question
            questionElement.textContent = currentQuestion.question;
            
            // Clear previous options
            optionsContainer.innerHTML = '';
            
            // Display each option
            currentQuestion.options.forEach((option, index) => {
                const optionDiv = document.createElement('div');
                optionDiv.className = 'options';
                
                const radioInput = document.createElement('input');
                radioInput.type = 'radio';
                radioInput.name = 'opt';
                radioInput.id = 'opt' + index;
                radioInput.value = index;
                
                const label = document.createElement('label');
                label.htmlFor = 'opt' + index;
                label.textContent = option;
                
                optionDiv.appendChild(radioInput);
                optionDiv.appendChild(label);
                optionsContainer.appendChild(optionDiv);
            });
            
            // Update score display
            updateScoreDisplay();
        } else {
            // No more questions
            questionElement.textContent = "Quiz completed!";
            optionsContainer.innerHTML = '';
            nextButton.disabled = true;
            showFinalScore();
        }

        
    }

// Function to update the score display
function updateScoreDisplay() {
    //scoreDisplay.textContent = `Score: ${score}/${questions.length}`;
}

// Function to show final score in a modal
function showFinalScore() {
    const percentage = Math.round((score / questions.length) * 100);
    let message= `<h2>Congrats</h2> <br>`;
    
    if (percentage >= 80) {
        message += "Excellent! <br> ⭐⭐⭐";
    } else if (percentage >= 60) {
        message += "Good job! <br> ⭐⭐";
         stars = '⭐⭐';
    } else if (percentage >= 40) {
        message += "Not bad! <br> ⭐";
    } else {
        message += "Keep learning! <br> ⭐";
    }
    
    // Create modal overlay
    const modalOverlay = document.createElement('div');
    modalOverlay.style.position = 'fixed';
    modalOverlay.style.top = '0';
    modalOverlay.style.left = '0';
    modalOverlay.style.width = '100%';
    modalOverlay.style.height = '100%';
    modalOverlay.style.backgroundColor = 'rgba(0,0,0,0.5)';
    modalOverlay.style.display = 'flex';
    modalOverlay.style.justifyContent = 'center';
    modalOverlay.style.alignItems = 'center';
    modalOverlay.style.zIndex = '1000';
    modalOverlay.id = 'modal-overlay';
    
    // Create modal content
    const modalContent = document.createElement('div');
    modalContent.style.backgroundColor = 'rgb(11, 11, 35)';
    modalContent.style.padding = '2rem';
    modalContent.style.borderRadius = '8px';
    modalContent.style.boxShadow = '0 4px 8px rgba(0,0,0,0.2)';
    modalContent.style.maxWidth = '500px';
    modalContent.style.width = '80%';
    modalContent.style.textAlign = 'center';
    
    // Add message to modal
    const scoreMessage = document.createElement('p');
    scoreMessage.innerHTML = message;
    scoreMessage.style.fontSize = "1.5em";
    scoreMessage.style.fontFamily = 'Arial, sans-serif';
    scoreMessage.style.marginBottom = '1.5rem';
    scoreMessage.style.color = 'white';
    
    // Add close button
    const closeButton = document.createElement('button');
    closeButton.textContent = 'OK';
    closeButton.style.padding = '0.5rem 1.5rem';
    closeButton.style.backgroundColor = '#4CAF50';
    closeButton.style.color = 'white';
    closeButton.style.border = 'none';
    closeButton.style.borderRadius = '4px';
    closeButton.style.cursor = 'pointer';
    closeButton.style.fontSize = '1rem';
    closeButton.addEventListener('click', function() {
        window.location.href = "mainpage.html";
    }); 
    
    // Add event listener to close modal
    closeButton.addEventListener('click', function() {
        document.body.removeChild(modalOverlay);
        // Re-enable the next button if you want to allow restarting
        nextButton.disabled = false;
    });
    
    // Assemble modal
    modalContent.appendChild(scoreMessage);
    modalContent.appendChild(closeButton);
    modalOverlay.appendChild(modalContent);
    
    // Disable all other elements on the page
    const allElements = document.querySelectorAll('body > *:not(div)');
    allElements.forEach(element => {
        element.style.pointerEvents = 'none'; // Disable interactions
        element.style.filter = 'blur(2px)'; // Optional: Add a blur effect
    });

    // Add modal to the page
    document.body.appendChild(modalOverlay);
    
    // Add event listener to handle ESC key
    modalOverlay.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            document.body.removeChild(modalOverlay);
            // Enable interactions on the page
            const allElements = document.querySelectorAll('body > *:not(div)');
            allElements.forEach(element => {
                element.style.pointerEvents = 'auto';
                element.style.filter = 'none';
            });
        }
    });
    
    // Focus the close button for accessibility
    closeButton.focus();
}

// Function to check the selected answer and move to next question
function checkAnswerAndMoveNext() {
    const selectedOption = document.querySelector('input[name="opt"]:checked');
    
    if (!selectedOption) {
        alert('Please select an answer!');
        return false;
    }
    
    const selectedAnswer = parseInt(selectedOption.value);
    const currentQuestion = questions[currentQuestionIndex];
    
    // Record whether answer was correct
    const isCorrect = selectedAnswer === currentQuestion.correctAnswer;
    
    if (isCorrect) {
        score++;
    } else {
        decreaseHealth();
    }
    
    // Always move to next question
    currentQuestionIndex++;
    
    if (currentQuestionIndex < questions.length) {
        displayQuestion();
    } else {
        // Quiz completed
        showFinalScore();
    }
    
    return isCorrect;
}

// Update the event listener for the next button
if (nextButton) {
    nextButton.addEventListener('click', checkAnswerAndMoveNext);
}

    // Function to decrease health
    function decreaseHealth() {
        const healthBar = document.querySelector('.health-bar');
        const hitBar = document.querySelector('.hit');
        let currentHealth = parseInt(healthBar.dataset.value);
        
        // Decrease health by 10%
        currentHealth = Math.max(0, currentHealth - 20);
        healthBar.dataset.value = currentHealth;
        hitBar.style.width = `${currentHealth}%`;
        
        // Change color based on health level
        if (currentHealth <= 20) {
            hitBar.style.backgroundColor = '#ff0000';
        } else if (currentHealth <= 50) {
            hitBar.style.backgroundColor = '#ffa500';
        }
        
        if (currentHealth <= 0) {
            showGameOver();
            return false;
        }
        return true;
    }

    function showGameOver() {
        // Create the overlay element
        const overlay = document.createElement('div');
        overlay.classList.add('overlay');
        document.body.appendChild(overlay);


        // Create modal overlay
        const modalOverlay = document.createElement('div');
        modalOverlay.style.position = 'fixed';
        modalOverlay.style.top = '0';
        modalOverlay.style.left = '0';
        modalOverlay.style.width = '100%';
        modalOverlay.style.height = '100%';
        modalOverlay.style.backgroundColor = 'rgba(11, 11, 35, 0.9)';
        modalOverlay.style.display = 'flex';
        modalOverlay.style.justifyContent = 'center';
        modalOverlay.style.alignItems = 'center';
        modalOverlay.style.zIndex = '1000';
        modalOverlay.id = 'modal-overlay';
        modalOverlay.classList.add('overlay');
    
        // Create modal content
        const modalContent = document.createElement('div');
        modalContent.style.backgroundColor = 'rgb(11, 11, 35)';
        modalContent.style.padding = '2rem';
        modalContent.style.borderRadius = '8px';
        modalContent.style.boxShadow = '0 0 4px rgba(203, 193, 193, 0.48)   ';
        modalContent.style.maxWidth = '500px';
        modalContent.style.width = '80%';
        modalContent.style.textAlign = 'center';
    
        // Add title
        const title = document.createElement('h2');
        title.textContent = 'Game Over!';
        title.style.color = 'red';
        title.style.marginBottom = '1rem';
        title.style.fontFamily = 'Arial, sans-serif';
    
        // Add score message
        const scoreMessage = document.createElement('p');
        scoreMessage.textContent = `Your final score: ${score}/${questions.length}`;
        scoreMessage.style.fontFamily = 'Arial, sans-serif';
        scoreMessage.style.marginBottom = '1.5rem';
        scoreMessage.style.color = 'White';
    
        // Add restart button
        const restartButton = document.createElement('button');
        restartButton.textContent = 'Restart Quiz';
        restartButton.id = 'restart-btn';
        restartButton.style.padding = '0.5rem 1.5rem';
        restartButton.style.backgroundColor = '#1a7de8';
        restartButton.style.color = 'white';
        restartButton.style.border = 'none';
        restartButton.style.borderRadius = '4px';
        restartButton.style.cursor = 'pointer';
        restartButton.style.fontSize = '1rem';
        restartButton.style.marginTop = '1rem';
    
        // Assemble modal
        modalContent.appendChild(title);
        modalContent.appendChild(scoreMessage);
        modalContent.appendChild(restartButton);
        modalOverlay.appendChild(modalContent);
    
        // Add to document
        document.body.appendChild(modalOverlay);
    
        // Add event listener
        restartButton.addEventListener('click', restartQuiz);
    }

    function restartQuiz() {
        currentQuestionIndex = 0;
        score = 0;
        
        // Reset health bar
        const healthBar = document.querySelector('.health-bar');
        const hitBar = document.querySelector('.hit');
        healthBar.dataset.value = '100';
        hitBar.style.width = '100%';
        hitBar.style.backgroundColor = '';
        
        // Remove any existing modals
        const modal = document.querySelector('#modal-overlay');
        if (modal) modal.remove();

        const modaloverlay = document.querySelector('.overlay');
        if (modal) modaloverlay.remove();
        
        const gameOverModal = document.querySelector('[style*="position:fixed; top:0; left:0"]');
        if (gameOverModal) gameOverModal.remove();
        
        // Reset UI
        nextButton.disabled = false;
        displayQuestion();
    }

    // Initialize the quiz
    displayQuestion();
});

// Function to show the instruction modal
function showInstructionModal() {
    // Create the overlay element
    const overlay = document.createElement('div');
    overlay.classList.add('overlay');
    document.body.appendChild(overlay);

    // Create the modal element
    const modal = document.createElement('div');
    modal.classList.add('modal');
    modal.style.textAlign = 'center';
    modal.style.backgroundColor = "#0b0b23";
    modal.style.color = "rgb(255, 255, 255)";
    modal.style.padding = "20px";
    modal.style.boxShadow = "0 0 4px rgba(203, 193, 193, 0.48)";
    modal.style.maxWidth = "600px";
    modal.style.margin = "auto";

    // Add the title to the modal
    const title = document.createElement('h2');
    title.textContent = 'Mission';
    title.style.color = "gold";
    title.style.fontSize = "1.5em";
    title.style.marginBottom = "20px";
    modal.appendChild(title);

    // Add the instructions content
    const instructions = document.createElement('div');
    instructions.style.textAlign = 'left';
    instructions.style.marginBottom = '20px';
    instructions.innerHTML = `
        <h3>Quiz Challenge</h3><br>
            <h4>📚 How to Play:</h4><br>
                <p><span class="emoji">❓</span> Answer multiple-choice questions about network cables</p><br>
                <p><span class="emoji">✅</span> <strong>Correct answer?</strong> Move to the next question!</p><br>
                <p><span class="emoji">❌</span> <strong>Wrong answer?</strong> Lose a life point</p><br>
                <p><span class="emoji">🏆</span> <strong>Goal:</strong> Survive all questions!</p><br>

            <p><span class="emoji">💡</span> <strong>Pro Tip:</strong> Read carefully!</p>
    `;
    instructions.style.fontFamily = "Arial";
    instructions.style.fontSize = "1.3em";
    modal.appendChild(instructions);

    // Add an "Ok" button to the modal
    const okButton = document.createElement('button');
    okButton.textContent = 'Start Game';
    okButton.style.marginTop = '10px';
    okButton.style.padding = '10px 20px';
    okButton.style.borderRadius = '5px';
    okButton.style.border = 'none';
    okButton.style.fontSize = '1em';
    okButton.style.backgroundColor = '#007BFF';
    okButton.style.color = 'white';
    okButton.style.cursor = 'pointer';
    okButton.addEventListener('click', () => {
        // Remove the modal and overlay when the "Ok" button is clicked
        document.body.removeChild(modal);
        document.body.removeChild(overlay);
        
        // Enable interactions on the page
        const allElements = document.querySelectorAll('body > *:not(div)');
        allElements.forEach(element => {
            element.style.pointerEvents = 'auto';
            element.style.filter = 'none';
        });
    });
    modal.appendChild(okButton);

    // Append the modal to the body
    document.body.appendChild(modal);

    // Disable all other elements on the page temporarily
    const allElements = document.querySelectorAll('body > *:not(div)');
    allElements.forEach(element => {
        element.style.pointerEvents = 'none';
        element.style.filter = 'blur(2px)';
    });
}